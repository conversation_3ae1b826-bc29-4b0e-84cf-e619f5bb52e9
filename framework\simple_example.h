#ifndef SIMPLE_EXAMPLE_H
#define SIMPLE_EXAMPLE_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QTextEdit>
#include <QTimer>
#include "simple_ui_binder.h"

/**
 * @brief 简单数据绑定示例
 * 
 * 演示简化版数据绑定框架的使用方法
 */
class SimpleExample : public QWidget
{
    Q_OBJECT

public:
    explicit SimpleExample(QWidget *parent = nullptr);
    ~SimpleExample();

private slots:
    /**
     * @brief 处理绑定初始化完成
     * @param controlCount 控件数量
     * @param controls 控件信息列表
     */
    void onBindingInitialized(int controlCount, const QList<SimpleControlInfo>& controls);

    /**
     * @brief 处理数据变化
     * @param index 控件序号
     * @param name 控件名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void onDataChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue);

    /**
     * @brief 显示控件表
     */
    void showControlTable();

    /**
     * @brief 模拟外部更新
     */
    void simulateExternalUpdate();

    /**
     * @brief 重置所有控件
     */
    void resetAllControls();

    /**
     * @brief 定时更新模拟数据
     */
    void updateSimulatedData();

private:
    /**
     * @brief 创建UI界面
     */
    void createUI();

    /**
     * @brief 初始化数据绑定
     */
    void initializeBinding();

    /**
     * @brief 创建控件组
     */
    QGroupBox* createControlGroup();

    /**
     * @brief 创建操作按钮组
     */
    QGroupBox* createButtonGroup();

    /**
     * @brief 创建日志显示组
     */
    QGroupBox* createLogGroup();

private:
    // UI控件
    QLineEdit* m_nameEdit;
    QComboBox* m_typeCombo;
    QCheckBox* m_enabledCheck;
    QSpinBox* m_countSpin;
    QDoubleSpinBox* m_valueSpin;
    QSlider* m_progressSlider;

    // 操作按钮
    QPushButton* m_showTableButton;
    QPushButton* m_updateButton;
    QPushButton* m_resetButton;

    // 日志显示
    QTextEdit* m_logDisplay;

    // 数据绑定器
    SimpleUIBinder* m_binder;

    // 模拟定时器
    QTimer* m_simulationTimer;
    int m_updateCounter;
};

#endif // SIMPLE_EXAMPLE_H
