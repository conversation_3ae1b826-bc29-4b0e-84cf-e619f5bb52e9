#ifndef SIMPLE_MAINWINDOW_H
#define SIMPLE_MAINWINDOW_H

#include <QMainWindow>
#include <QTabWidget>
#include <QMenuBar>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTextEdit>
#include <QGroupBox>

// 包含现有的界面类
#include "gripperprocess.h"
#include "gripperprocessparam.h"
#include "screwingprocess.h"
#include "screwingprocessparam.h"

// 包含简单框架示例
#include "framework/simple_example.h"
#include "framework/enhanced_gripper_simple.h"
#include "framework/inherited_example.h"
#include "framework/bindable_gripper_process.h"

/**
 * @brief 简化的主窗口类
 * 
 * 展示简单数据绑定框架的集成效果
 */
class SimpleMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    SimpleMainWindow(QWidget *parent = nullptr);
    ~SimpleMainWindow();

private slots:
    /**
     * @brief 显示关于信息
     */
    void showAbout();

    /**
     * @brief 切换标签页
     * @param index 标签页索引
     */
    void onTabChanged(int index);

    /**
     * @brief 处理增强版夹爪界面的信号
     */
    void onGripperInitialized(int controlCount, const QList<SimpleControlInfo>& controls);
    void onGripperParameterChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue);
    void onGripperOperationRequested(const QString& operation, const QVariant& parameters);

private:
    /**
     * @brief 创建界面
     */
    void createUI();

    /**
     * @brief 创建菜单
     */
    void createMenus();

    /**
     * @brief 创建状态栏
     */
    void createStatusBar();

    /**
     * @brief 创建日志面板
     */
    QWidget* createLogPanel();

private:
    // 中央控件
    QTabWidget* m_tabWidget;

    // 各个界面
    GripperProcess* m_gripperProcess;
    GripperProcessParam* m_gripperProcessParam;
    ScrewingProcess* m_screwingProcess;
    ScrewingProcessParam* m_screwingProcessParam;
    SimpleExample* m_simpleExample;
    EnhancedGripperSimple* m_enhancedGripper;
    InheritedExample* m_inheritedExample;
    BindableGripperProcess* m_bindableGripper;

    // 日志显示
    QTextEdit* m_logDisplay;

    // 状态栏
    QLabel* m_statusLabel;
    QLabel* m_currentTabLabel;
};

#endif // SIMPLE_MAINWINDOW_H
