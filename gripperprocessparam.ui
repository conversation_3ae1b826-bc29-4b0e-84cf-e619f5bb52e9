<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GripperProcessParam</class>
 <widget class="QWidget" name="GripperProcessParam">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>899</width>
    <height>466</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(240, 248, 255, 255),
        stop:1 rgba(230, 240, 250, 255));
font: 16px &quot;Microsoft YaHei&quot;;
}

QPushButton {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(80, 160, 240, 255),   /* 提亮蓝起点 */
        stop:1 rgba(60, 140, 220, 255));  /* 提亮蓝终点 */
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: white;
    padding: 6px;
        font: 14px &quot;Microsoft YaHei&quot;;
    font-weight: bold;
    border-radius: 10px;
    /*min-height: 28px;*/
}

QPushButton:hover {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(100, 175, 250, 255),
        stop:1 rgba(80, 160, 240, 255));
}

QPushButton:pressed {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(50, 130, 210, 255),
        stop:1 rgba(40, 110, 190, 255));
}


/* tabBar样式设置*/
QTabWidget::pane {
background: #ffffff;
border: none;
border-top: 2px solid #005bac;
}
QTabWidget::tab-bar {
alignment: left;
}
QTabBar::tab{
height: 30px;
width:100px;
background: #ededed;
border: 1px solid;
border-color: #cdcdcd;
border-bottom: none;
font-family: Microsoft YaHei;
color: #595959;
font-size: 18px;
line-height: 20px;
}
QTabBar::tab:selected {
border-color: #b8b8b8;
background : #005bac;
font-family: Microsoft YaHei;
color: #ffffff;
font-size: 18px;
line-height: 20px;
}
QTabBar::close-button {
image: url(:/image/close_12px.png);
subcontrol-position: right;
}
QTabBar::close-button:hover {
image: url(:/image/close_12px_w.png);
}
QTabBar::close-button:selected {
image: url(:/image/close_12px_w.png);
}

QComboBox {
    background-color: #F0F8FF; /* 淡雅的浅蓝色，与背景协调 */
    color: #1F354D;             /* 深蓝灰，易读 */
    border: 1px solid #A4B4C8;  /* 柔和边框色 */
    border-radius: 4px;
    padding: 6px 12px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #14C9E2;  /* 亮蓝高亮色 */
}

QComboBox:focus {
    border-color: #005BAC;  /* 深蓝聚焦色 */
}

QComboBox::drop-down {
    background-color: #E6F0FA;
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
        image: url(:/img/img/down_arrow_hd.png);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #F7FBFF;      /* 很浅的蓝白色 */
    color: #1F354D;                 /* 保持可读性 */
    border: 1px solid #A4B4C8;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #BBDFFF; /* 柔和的选中背景 */
    selection-color: #0A2647;            /* 深蓝选中文字 */
}


QSpinBox, QDoubleSpinBox {
    background-color: #F0F8FF;           /* 与背景接近但稍微深一点 */
    color: #1F354D;                      /* 深蓝灰字体 */
    border: 1px solid #A4B4C8;           /* 柔和边框 */
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 24px;
    font-weight: 500;
}

/* 鼠标悬停时边框颜色高亮 */
QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #14C9E2;
}

/* 焦点状态边框加深 */
QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #005BAC;
}

/* 按钮区域样式（上下箭头） */
QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #E6F0FA;
    border: none;
    width: 16px;
    padding: 2px;
    subcontrol-origin: border;
}

/* 鼠标悬停按钮时颜色稍亮 */
QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #D0E4F5;
}

/* 按钮按下状态颜色加深 */
QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #BBDFFF;
}

/* 禁用状态 */
QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #D6E5F2;
    color: #9BAEC3;
    border-color: #B0C4D8;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow{
        width: 10px;
    height: 10px;
        image: url(:/img/img/up_arrow_hd.png);
}
QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    width: 10px;
    height: 10px;
        image: url(:/img/img/down_arrow_hd.png);
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    transform: rotate(180deg); /* 向下 */
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_8">
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>基本参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0" colspan="2">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_36">
           <property name="text">
            <string>工程选择:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_6"/>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="groupBox_3">
         <property name="title">
          <string>夹电源工艺参数</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="0">
           <widget class="QTabWidget" name="tabWidget_3">
            <property name="currentIndex">
             <number>0</number>
            </property>
            <widget class="QWidget" name="tab_2">
             <attribute name="title">
              <string>用户参数</string>
             </attribute>
             <layout class="QVBoxLayout" name="verticalLayout">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_49">
                <item>
                 <widget class="QLabel" name="label_9">
                  <property name="text">
                   <string>夹爪舵机:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_15"/>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_52">
                <item>
                 <widget class="QLabel" name="label_17">
                  <property name="text">
                   <string>夹紧距离:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_17"/>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_50">
                <item>
                 <widget class="QLabel" name="label_16">
                  <property name="text">
                   <string>夹紧速度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_16"/>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_53">
                <item>
                 <widget class="QLabel" name="label_18">
                  <property name="text">
                   <string>松开距离:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_18"/>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_56">
                <item>
                 <widget class="QLabel" name="label_21">
                  <property name="text">
                   <string>松开速度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_21"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_65">
            <item>
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_11">
              <property name="text">
               <string>刷新</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_12">
              <property name="text">
               <string>设置</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QGroupBox" name="groupBox_8">
         <property name="title">
          <string>轴参数设置</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_24">
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_22">
              <property name="text">
               <string>刷新</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_21">
              <property name="text">
               <string>设置</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="0">
           <widget class="QTabWidget" name="tabWidget_2">
            <property name="currentIndex">
             <number>0</number>
            </property>
            <widget class="QWidget" name="tab_3">
             <attribute name="title">
              <string>Z1</string>
             </attribute>
             <layout class="QGridLayout" name="gridLayout_6">
              <item row="0" column="1" colspan="3">
               <layout class="QHBoxLayout" name="horizontalLayout_7">
                <item>
                 <widget class="QLabel" name="label_32">
                  <property name="text">
                   <string>丝杆螺距:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_26"/>
                </item>
               </layout>
              </item>
              <item row="1" column="0" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_8">
                <item>
                 <widget class="QLabel" name="label_33">
                  <property name="text">
                   <string>气压检测比较值:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_27"/>
                </item>
               </layout>
              </item>
              <item row="2" column="3">
               <layout class="QHBoxLayout" name="horizontalLayout_22">
                <item>
                 <widget class="QLabel" name="label_39">
                  <property name="text">
                   <string>流程下降速度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_31"/>
                </item>
               </layout>
              </item>
              <item row="3" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_23">
                <item>
                 <widget class="QLabel" name="label_40">
                  <property name="text">
                   <string>点动速度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_32"/>
                </item>
               </layout>
              </item>
              <item row="1" column="2" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <item>
                 <widget class="QLabel" name="label_34">
                  <property name="text">
                   <string>气缸吸取保持时间:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_28"/>
                </item>
               </layout>
              </item>
              <item row="0" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_6">
                <item>
                 <widget class="QLabel" name="label_31">
                  <property name="text">
                   <string>零点偏移:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_25"/>
                </item>
               </layout>
              </item>
              <item row="3" column="1" colspan="3">
               <layout class="QHBoxLayout" name="horizontalLayout_25">
                <item>
                 <widget class="QLabel" name="label_41">
                  <property name="text">
                   <string>点动位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_33"/>
                </item>
               </layout>
              </item>
              <item row="2" column="0" colspan="3">
               <layout class="QHBoxLayout" name="horizontalLayout_10">
                <item>
                 <widget class="QLabel" name="label_35">
                  <property name="text">
                   <string>气缸放料保持时间:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_29"/>
                </item>
               </layout>
              </item>
              <item row="4" column="0">
               <spacer name="verticalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="tab_5">
             <attribute name="title">
              <string>U1</string>
             </attribute>
             <layout class="QGridLayout" name="gridLayout_7">
              <item row="0" column="1" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_37">
                <item>
                 <widget class="QLabel" name="label_49">
                  <property name="text">
                   <string>目标角度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_42"/>
                </item>
               </layout>
              </item>
              <item row="0" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_40">
                <item>
                 <widget class="QLabel" name="label_52">
                  <property name="text">
                   <string>当前绝对位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_45"/>
                </item>
               </layout>
              </item>
              <item row="1" column="0" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_38">
                <item>
                 <widget class="QLabel" name="label_50">
                  <property name="text">
                   <string>电机方向(1正,2反):</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_43"/>
                </item>
               </layout>
              </item>
              <item row="1" column="2">
               <layout class="QHBoxLayout" name="horizontalLayout_39">
                <item>
                 <widget class="QLabel" name="label_51">
                  <property name="text">
                   <string>复位后偏移角度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_44"/>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <spacer name="verticalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="tab_7">
             <attribute name="title">
              <string>相机</string>
             </attribute>
             <layout class="QGridLayout" name="gridLayout_5">
              <item row="0" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_45">
                <item>
                 <widget class="QLabel" name="label_57">
                  <property name="text">
                   <string>当前绝对位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_50"/>
                </item>
               </layout>
              </item>
              <item row="0" column="1" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_47">
                <item>
                 <widget class="QLabel" name="label_59">
                  <property name="text">
                   <string>目标位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_52"/>
                </item>
               </layout>
              </item>
              <item row="1" column="0" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_48">
                <item>
                 <widget class="QLabel" name="label_60">
                  <property name="text">
                   <string>电机方向(1正,2反):</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_53"/>
                </item>
               </layout>
              </item>
              <item row="1" column="2">
               <layout class="QHBoxLayout" name="horizontalLayout_46">
                <item>
                 <widget class="QLabel" name="label_58">
                  <property name="text">
                   <string>复位后偏移角度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_51"/>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <spacer name="verticalSpacer_4">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="tab_4">
             <attribute name="title">
              <string>舵机</string>
             </attribute>
             <layout class="QGridLayout" name="gridLayout_4">
              <item row="0" column="1" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_14">
                <item>
                 <widget class="QLabel" name="label_44">
                  <property name="text">
                   <string>舵机限位:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_37"/>
                </item>
               </layout>
              </item>
              <item row="3" column="0" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_26">
                <item>
                 <widget class="QLabel" name="label_42">
                  <property name="text">
                   <string>485复位位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_34"/>
                </item>
               </layout>
              </item>
              <item row="2" column="1" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_27">
                <item>
                 <widget class="QLabel" name="label_46">
                  <property name="text">
                   <string>SDO复位位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_39"/>
                </item>
               </layout>
              </item>
              <item row="1" column="1" colspan="2">
               <layout class="QHBoxLayout" name="horizontalLayout_12">
                <item>
                 <widget class="QLabel" name="label_38">
                  <property name="text">
                   <string>目标位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_35"/>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_11">
                <item>
                 <widget class="QLabel" name="label_37">
                  <property name="text">
                   <string>当前位置:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_30"/>
                </item>
               </layout>
              </item>
              <item row="1" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_13">
                <item>
                 <widget class="QLabel" name="label_43">
                  <property name="text">
                   <string>舵机速度:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_36"/>
                </item>
               </layout>
              </item>
              <item row="3" column="2">
               <layout class="QHBoxLayout" name="horizontalLayout_28">
                <item>
                 <widget class="QLabel" name="label_47">
                  <property name="text">
                   <string>移动状态:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_40"/>
                </item>
               </layout>
              </item>
              <item row="0" column="0">
               <layout class="QHBoxLayout" name="horizontalLayout_15">
                <item>
                 <widget class="QLabel" name="label_45">
                  <property name="text">
                   <string>舵机原点:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_38"/>
                </item>
               </layout>
              </item>
              <item row="4" column="0">
               <spacer name="verticalSpacer">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>157</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
