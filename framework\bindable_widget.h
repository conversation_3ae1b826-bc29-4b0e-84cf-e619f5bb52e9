#ifndef BINDABLE_WIDGET_H
#define BINDABLE_WIDGET_H

#include <QWidget>
#include <QVariant>
#include <QMap>
#include <QList>
#include <QMetaObject>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QRadioButton>

/**
 * @brief 控件绑定信息结构
 */
struct ControlBindInfo {
    int index;              // 控件序号
    QWidget* widget;        // 控件指针
    QString name;           // 控件名称
    QString type;           // 控件类型
    QVariant value;         // 当前值
    QMetaObject::Connection connection; // 信号连接
    
    ControlBindInfo() : index(-1), widget(nullptr) {}
};

/**
 * @brief 可绑定的Widget基类
 * 
 * 通过继承这个类，任何QWidget都可以自动获得数据绑定功能：
 * 1. 自动扫描并绑定所有子控件
 * 2. 自动分配控件序号
 * 3. 控件数据变化时自动发送信号
 * 4. 提供外部数据更新接口
 * 
 * 使用方法：
 * 1. 让您的界面类继承BindableWidget而不是QWidget
 * 2. 在构造函数中调用initializeBinding()
 * 3. 重写onDataChanged()处理数据变化
 */
class BindableWidget : public QWidget
{
    Q_OBJECT

public:
    explicit BindableWidget(QWidget *parent = nullptr);
    virtual ~BindableWidget();

protected:
    /**
     * @brief 初始化数据绑定
     * 在子类构造函数中调用此方法来启动自动绑定
     * @param autoConnect 是否自动连接信号槽（默认true）
     * @return 绑定的控件数量
     */
    int initializeBinding(bool autoConnect = true);

    /**
     * @brief 数据变化处理函数（子类重写）
     * 当任何绑定的控件数据发生变化时会调用此函数
     * @param index 控件序号
     * @param name 控件名称
     * @param newValue 新值
     * @param oldValue 旧值
     */
    virtual void onDataChanged(int index, const QString& name, 
                              const QVariant& newValue, const QVariant& oldValue);

    /**
     * @brief 绑定初始化完成处理函数（子类重写）
     * 当绑定初始化完成时会调用此函数
     * @param controlCount 绑定的控件数量
     * @param controls 控件信息列表
     */
    virtual void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls);

public:
    /**
     * @brief 通过序号更新控件值
     * @param index 控件序号
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateControlValue(int index, const QVariant& value);

    /**
     * @brief 通过控件名更新控件值
     * @param name 控件名称
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateControlValueByName(const QString& name, const QVariant& value);

    /**
     * @brief 批量更新控件值
     * @param values 序号到值的映射
     * @return 成功更新的控件数量
     */
    int updateMultipleValues(const QMap<int, QVariant>& values);

    /**
     * @brief 获取控件当前值
     * @param index 控件序号
     * @return 当前值
     */
    QVariant getControlValue(int index) const;

    /**
     * @brief 获取控件信息
     * @param index 控件序号
     * @return 控件信息
     */
    ControlBindInfo getControlInfo(int index) const;

    /**
     * @brief 获取所有控件信息
     * @return 控件信息列表
     */
    QList<ControlBindInfo> getAllControls() const;

    /**
     * @brief 获取所有控件的当前值
     * @return 序号到值的映射
     */
    QMap<int, QVariant> getAllValues() const;

    /**
     * @brief 根据控件名查找序号
     * @param name 控件名称
     * @return 控件序号，未找到返回-1
     */
    int findControlIndex(const QString& name) const;

    /**
     * @brief 获取绑定的控件总数
     * @return 控件数量
     */
    int getControlCount() const { return m_controls.size(); }

    /**
     * @brief 检查是否已初始化绑定
     * @return 是否已初始化
     */
    bool isBindingInitialized() const { return m_bindingInitialized; }

signals:
    /**
     * @brief 控件数据变化信号
     * @param index 控件序号
     * @param name 控件名称
     * @param newValue 新值
     * @param oldValue 旧值
     */
    void controlDataChanged(int index, const QString& name, 
                           const QVariant& newValue, const QVariant& oldValue);

    /**
     * @brief 绑定初始化完成信号
     * @param controlCount 绑定的控件数量
     * @param controls 控件信息列表
     */
    void bindingInitialized(int controlCount, const QList<ControlBindInfo>& controls);

private slots:
    /**
     * @brief 处理控件值变化的统一槽函数
     */
    void handleControlValueChanged();

private:
    /**
     * @brief 递归扫描控件
     * @param widget 要扫描的控件
     */
    void scanControls(QWidget* widget);

    /**
     * @brief 检查控件是否支持绑定
     * @param widget 控件指针
     * @return 是否支持
     */
    bool isSupportedControl(QWidget* widget) const;

    /**
     * @brief 获取控件类型名称
     * @param widget 控件指针
     * @return 类型名称
     */
    QString getControlTypeName(QWidget* widget) const;

    /**
     * @brief 连接控件信号
     * @param info 控件信息
     * @return 是否连接成功
     */
    bool connectControlSignal(ControlBindInfo& info);

    /**
     * @brief 断开控件信号连接
     * @param info 控件信息
     */
    void disconnectControlSignal(ControlBindInfo& info);

    /**
     * @brief 获取控件当前值
     * @param widget 控件指针
     * @return 当前值
     */
    QVariant getWidgetCurrentValue(QWidget* widget) const;

    /**
     * @brief 设置控件值
     * @param widget 控件指针
     * @param value 要设置的值
     * @return 是否设置成功
     */
    bool setWidgetValue(QWidget* widget, const QVariant& value);

private:
    QList<ControlBindInfo> m_controls;          // 控件信息列表
    QMap<QString, int> m_nameToIndex;           // 控件名到序号的映射
    QMap<QWidget*, int> m_widgetToIndex;        // 控件指针到序号的映射
    bool m_bindingInitialized;                  // 是否已初始化绑定
};

#endif // BINDABLE_WIDGET_H
