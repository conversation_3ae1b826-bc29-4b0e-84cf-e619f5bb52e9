#include "simple_ui_binder.h"
#include <QDebug>

SimpleUIBinder::SimpleUIBinder(QWidget* parentWidget, QObject *parent)
    : QObject(parent)
    , m_parentWidget(parentWidget)
    , m_initialized(false)
{
    if (!m_parentWidget) {
        qWarning() << "SimpleUIBinder: parentWidget is null!";
    }
}

SimpleUIBinder::~SimpleUIBinder()
{
    // 断开所有信号连接
    for (auto& info : m_controls) {
        disconnectWidgetSignal(info);
    }
}

int SimpleUIBinder::initialize()
{
    if (!m_parentWidget) {
        qWarning() << "SimpleUIBinder: Cannot initialize without parent widget";
        return 0;
    }

    // 清除现有数据
    for (auto& info : m_controls) {
        disconnectWidgetSignal(info);
    }
    m_controls.clear();
    m_nameToIndex.clear();
    m_widgetToIndex.clear();

    // 扫描所有控件
    scanWidget(m_parentWidget);

    // 连接信号
    for (auto& info : m_controls) {
        connectWidgetSignal(info);
    }

    m_initialized = true;
    
    qDebug() << "SimpleUIBinder: Initialized" << m_controls.size() << "controls";
    
    // 发送初始化完成信号
    emit initialized(m_controls.size(), m_controls);
    
    return m_controls.size();
}

void SimpleUIBinder::scanWidget(QWidget* widget)
{
    if (!widget) return;

    // 检查当前控件是否支持绑定
    if (isSupportedWidget(widget)) {
        SimpleControlInfo info;
        info.index = m_controls.size(); // 使用列表大小作为序号
        info.widget = widget;
        info.name = widget->objectName();
        info.type = getWidgetTypeName(widget);
        info.value = getWidgetValue(widget);
        
        // 添加到列表和映射表
        m_controls.append(info);
        if (!info.name.isEmpty()) {
            m_nameToIndex[info.name] = info.index;
        }
        m_widgetToIndex[widget] = info.index;
        
        qDebug() << "SimpleUIBinder: Found control [" << info.index << "]" 
                 << info.name << "(" << info.type << ") = " << info.value;
    }

    // 递归扫描子控件
    const QList<QWidget*> children = widget->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    for (QWidget* child : children) {
        scanWidget(child);
    }
}

bool SimpleUIBinder::isSupportedWidget(QWidget* widget) const
{
    if (!widget || widget->objectName().isEmpty()) {
        return false;
    }

    // 检查是否为支持的控件类型
    return qobject_cast<QLineEdit*>(widget) ||
           qobject_cast<QComboBox*>(widget) ||
           qobject_cast<QCheckBox*>(widget) ||
           qobject_cast<QRadioButton*>(widget) ||
           qobject_cast<QSpinBox*>(widget) ||
           qobject_cast<QDoubleSpinBox*>(widget) ||
           qobject_cast<QSlider*>(widget) ||
           qobject_cast<QTextEdit*>(widget) ||
           qobject_cast<QPlainTextEdit*>(widget);
}

QString SimpleUIBinder::getWidgetTypeName(QWidget* widget) const
{
    if (!widget) return "Unknown";
    return widget->metaObject()->className();
}

bool SimpleUIBinder::connectWidgetSignal(SimpleControlInfo& info)
{
    if (!info.widget) return false;

    QWidget* widget = info.widget;
    
    // 根据控件类型连接相应的信号
    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        connect(lineEdit, &QLineEdit::textChanged, this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        connect(comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        connect(checkBox, &QCheckBox::toggled, this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        connect(radioButton, &QRadioButton::toggled, this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        connect(doubleSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        connect(slider, &QSlider::valueChanged, this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        connect(textEdit, &QTextEdit::textChanged, this, &SimpleUIBinder::onControlValueChanged);
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        connect(plainTextEdit, &QPlainTextEdit::textChanged, this, &SimpleUIBinder::onControlValueChanged);
    }
    else {
        qWarning() << "SimpleUIBinder: Unsupported widget type:" << widget->metaObject()->className();
        return false;
    }

    return true;
}

void SimpleUIBinder::disconnectWidgetSignal(SimpleControlInfo& info)
{
    if (info.widget) {
        disconnect(info.widget, nullptr, this, nullptr);
    }
}

QVariant SimpleUIBinder::getWidgetValue(QWidget* widget) const
{
    if (!widget) return QVariant();

    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        return lineEdit->text();
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        return comboBox->currentIndex();
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        return checkBox->isChecked();
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        return radioButton->isChecked();
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        return spinBox->value();
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        return doubleSpinBox->value();
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        return slider->value();
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        return textEdit->toPlainText();
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        return plainTextEdit->toPlainText();
    }

    return QVariant();
}

bool SimpleUIBinder::setWidgetValue(QWidget* widget, const QVariant& value)
{
    if (!widget) return false;

    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        lineEdit->setText(value.toString());
        return true;
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        comboBox->setCurrentIndex(value.toInt());
        return true;
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        checkBox->setChecked(value.toBool());
        return true;
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        radioButton->setChecked(value.toBool());
        return true;
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        spinBox->setValue(value.toInt());
        return true;
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        doubleSpinBox->setValue(value.toDouble());
        return true;
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        slider->setValue(value.toInt());
        return true;
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        textEdit->setPlainText(value.toString());
        return true;
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        plainTextEdit->setPlainText(value.toString());
        return true;
    }

    return false;
}

void SimpleUIBinder::onControlValueChanged()
{
    QWidget* senderWidget = qobject_cast<QWidget*>(sender());
    if (!senderWidget) return;

    // 查找控件序号
    auto it = m_widgetToIndex.find(senderWidget);
    if (it == m_widgetToIndex.end()) return;

    int index = it.value();
    if (index < 0 || index >= m_controls.size()) return;

    SimpleControlInfo& info = m_controls[index];
    QVariant oldValue = info.value;
    QVariant newValue = getWidgetValue(senderWidget);

    // 更新存储的值
    info.value = newValue;

    // 发射数据变化信号
    emit dataChanged(index, info.name, newValue, oldValue);
}

bool SimpleUIBinder::updateValue(int index, const QVariant& value)
{
    if (index < 0 || index >= m_controls.size()) {
        qWarning() << "SimpleUIBinder: Invalid index" << index;
        return false;
    }

    SimpleControlInfo& info = m_controls[index];
    if (!info.widget) {
        qWarning() << "SimpleUIBinder: Widget is null for index" << index;
        return false;
    }

    QVariant oldValue = info.value;
    
    // 临时断开信号连接，避免循环触发
    disconnectWidgetSignal(info);
    
    // 设置控件值
    bool success = setWidgetValue(info.widget, value);
    
    if (success) {
        info.value = value;
        emit dataChanged(index, info.name, value, oldValue);
    }
    
    // 重新连接信号
    connectWidgetSignal(info);
    
    return success;
}

bool SimpleUIBinder::updateValueByName(const QString& name, const QVariant& value)
{
    int index = findIndexByName(name);
    if (index == -1) {
        qWarning() << "SimpleUIBinder: Control not found with name" << name;
        return false;
    }
    return updateValue(index, value);
}

QVariant SimpleUIBinder::getValue(int index) const
{
    if (index < 0 || index >= m_controls.size()) {
        return QVariant();
    }
    return m_controls[index].value;
}

SimpleControlInfo SimpleUIBinder::getControlInfo(int index) const
{
    if (index < 0 || index >= m_controls.size()) {
        return SimpleControlInfo();
    }
    return m_controls[index];
}

QList<SimpleControlInfo> SimpleUIBinder::getAllControls() const
{
    return m_controls;
}

int SimpleUIBinder::findIndexByName(const QString& name) const
{
    auto it = m_nameToIndex.find(name);
    if (it != m_nameToIndex.end()) {
        return it.value();
    }
    return -1;
}
