#include <QApplication>
#include <QMainWindow>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QGroupBox>
#include <QLabel>
#include <QStatusBar>
#include <QMenuBar>
#include <QMessageBox>
#include <QDebug>

#include "inherited_example.h"
#include "bindable_gripper_process.h"

/**
 * @brief 继承模式演示主窗口
 */
class InheritedMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    InheritedMainWindow(QWidget *parent = nullptr) : QMainWindow(parent) {
        setWindowTitle("基于继承的数据绑定框架演示");
        setMinimumSize(1000, 700);
        
        createUI();
        createMenus();
        createStatusBar();
        
        m_statusLabel->setText("就绪 - 基于继承的数据绑定框架");
    }

private slots:
    void showAbout() {
        QString aboutText = 
            "<h2>基于继承的数据绑定框架</h2>"
            "<p><b>版本:</b> 1.0.0</p>"
            "<p>这是一个基于继承模式的Qt UI数据绑定框架演示。</p>"
            "<h3>核心特性:</h3>"
            "<ul>"
            "<li><b>继承模式:</b> 通过继承BindableWidget实现自动绑定</li>"
            "<li><b>虚函数重写:</b> 重写onDataChanged()处理数据变化</li>"
            "<li><b>自动初始化:</b> 在构造函数中调用initializeBinding()</li>"
            "<li><b>信号槽通知:</b> 自动发射信号通知外部系统</li>"
            "</ul>"
            "<h3>使用方法:</h3>"
            "<ol>"
            "<li>让您的界面类继承BindableWidget而不是QWidget</li>"
            "<li>在构造函数中调用initializeBinding()</li>"
            "<li>重写onDataChanged()处理数据变化</li>"
            "<li>重写onBindingInitialized()处理初始化完成</li>"
            "</ol>"
            "<p>查看系统日志了解框架的工作过程。</p>";
        
        QMessageBox::about(this, "关于", aboutText);
    }
    
    void onTabChanged(int index) {
        QStringList tabNames = {"基础继承示例", "可绑定夹爪界面"};
        
        if (index >= 0 && index < tabNames.size()) {
            m_statusLabel->setText("当前: " + tabNames[index]);
            m_logDisplay->append(QString("=== 切换到: %1 ===").arg(tabNames[index]));
            
            if (index == 0) {
                m_logDisplay->append("这个标签页展示了基础的继承模式数据绑定");
            } else if (index == 1) {
                m_logDisplay->append("这个标签页展示了如何将现有界面改造为继承模式");
            }
        }
    }
    
    void onInheritedExampleDataChanged(int index, const QString& name, 
                                      const QVariant& value, const QVariant& oldValue) {
        m_logDisplay->append(QString("基础示例数据变化: [%1] %2: %3 -> %4")
                            .arg(index).arg(name).arg(oldValue.toString()).arg(value.toString()));
    }
    
    void onGripperParameterChanged(int index, const QString& name, 
                                  const QVariant& value, const QVariant& oldValue) {
        m_logDisplay->append(QString("夹爪参数变化: [%1] %2: %3 -> %4")
                            .arg(index).arg(name).arg(oldValue.toString()).arg(value.toString()));
    }
    
    void onGripperOperationRequested(const QString& operation, const QVariant& parameters) {
        m_logDisplay->append(QString("夹爪操作请求: %1").arg(operation));
        if (parameters.isValid() && parameters.type() == QVariant::Map) {
            QVariantMap paramMap = parameters.toMap();
            for (auto it = paramMap.begin(); it != paramMap.end(); ++it) {
                m_logDisplay->append(QString("  参数: %1 = %2").arg(it.key()).arg(it.value().toString()));
            }
        }
    }

private:
    void createUI() {
        // 创建中央控件
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        // 创建主布局
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 创建标签页控件
        m_tabWidget = new QTabWidget();
        connect(m_tabWidget, &QTabWidget::currentChanged, this, &InheritedMainWindow::onTabChanged);
        
        // 添加基础继承示例
        m_inheritedExample = new InheritedExample();
        m_tabWidget->addTab(m_inheritedExample, "基础继承示例");
        
        // 连接基础示例的信号
        connect(m_inheritedExample, &InheritedExample::controlDataChanged,
                this, &InheritedMainWindow::onInheritedExampleDataChanged);
        
        // 添加可绑定夹爪界面
        m_bindableGripper = new BindableGripperProcess();
        m_tabWidget->addTab(m_bindableGripper, "可绑定夹爪界面");
        
        // 连接夹爪界面的信号
        connect(m_bindableGripper, &BindableGripperProcess::gripperParameterChanged,
                this, &InheritedMainWindow::onGripperParameterChanged);
        connect(m_bindableGripper, &BindableGripperProcess::gripperOperationRequested,
                this, &InheritedMainWindow::onGripperOperationRequested);
        
        // 创建日志面板
        QGroupBox* logGroup = new QGroupBox("系统日志");
        QVBoxLayout* logLayout = new QVBoxLayout(logGroup);
        
        m_logDisplay = new QTextEdit();
        m_logDisplay->setMaximumHeight(200);
        m_logDisplay->setReadOnly(true);
        m_logDisplay->append("=== 基于继承的数据绑定框架演示启动 ===");
        m_logDisplay->append("所有界面都通过继承BindableWidget实现自动数据绑定");
        m_logDisplay->append("数据变化会自动通过虚函数和信号槽通知外部");
        
        logLayout->addWidget(m_logDisplay);
        
        // 添加到主布局
        mainLayout->addWidget(m_tabWidget, 3);
        mainLayout->addWidget(logGroup, 1);
    }
    
    void createMenus() {
        QMenu* helpMenu = menuBar()->addMenu("帮助(&H)");
        
        QAction* aboutAction = new QAction("关于(&A)", this);
        connect(aboutAction, &QAction::triggered, this, &InheritedMainWindow::showAbout);
        helpMenu->addAction(aboutAction);
    }
    
    void createStatusBar() {
        m_statusLabel = new QLabel("就绪");
        statusBar()->addWidget(m_statusLabel);
    }

private:
    QTabWidget* m_tabWidget;
    InheritedExample* m_inheritedExample;
    BindableGripperProcess* m_bindableGripper;
    QTextEdit* m_logDisplay;
    QLabel* m_statusLabel;
};

/**
 * @brief 继承模式演示程序入口
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    app.setApplicationName("基于继承的数据绑定框架");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Inherited UI Framework Demo");
    
    InheritedMainWindow window;
    window.show();
    
    return app.exec();
}

#include "inherited_main.moc"
