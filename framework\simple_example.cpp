#include "simple_example.h"
#include <QMessageBox>
#include <QDebug>

SimpleExample::SimpleExample(QWidget *parent)
    : QWidget(parent)
    , m_nameEdit(nullptr)
    , m_type<PERSON>ombo(nullptr)
    , m_enabled<PERSON>heck(nullptr)
    , m_count<PERSON><PERSON>(nullptr)
    , m_value<PERSON>pin(nullptr)
    , m_progressSlider(nullptr)
    , m_logDisplay(nullptr)
    , m_binder(nullptr)
    , m_simulationTimer(nullptr)
    , m_updateCounter(0)
{
    setWindowTitle("简单数据绑定框架示例");
    setMinimumSize(600, 500);
    
    createUI();
    initializeBinding();
    
    // 创建模拟定时器
    m_simulationTimer = new QTimer(this);
    connect(m_simulationTimer, &QTimer::timeout, this, &SimpleExample::updateSimulatedData);
    m_simulationTimer->start(3000); // 每3秒更新一次
}

SimpleExample::~SimpleExample()
{
    delete m_binder;
}

void SimpleExample::createUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    mainLayout->addWidget(createControlGroup());
    mainLayout->addWidget(createButtonGroup());
    mainLayout->addWidget(createLogGroup());
    
    setLayout(mainLayout);
}

QGroupBox* SimpleExample::createControlGroup()
{
    QGroupBox* group = new QGroupBox("测试控件");
    QGridLayout* layout = new QGridLayout(group);
    
    // 名称输入
    layout->addWidget(new QLabel("名称:"), 0, 0);
    m_nameEdit = new QLineEdit();
    m_nameEdit->setObjectName("nameEdit");
    m_nameEdit->setText("默认名称");
    layout->addWidget(m_nameEdit, 0, 1);
    
    // 类型选择
    layout->addWidget(new QLabel("类型:"), 1, 0);
    m_typeCombo = new QComboBox();
    m_typeCombo->setObjectName("typeCombo");
    m_typeCombo->addItems({"类型A", "类型B", "类型C"});
    layout->addWidget(m_typeCombo, 1, 1);
    
    // 启用状态
    m_enabledCheck = new QCheckBox("启用");
    m_enabledCheck->setObjectName("enabledCheck");
    m_enabledCheck->setChecked(true);
    layout->addWidget(m_enabledCheck, 2, 0, 1, 2);
    
    // 计数器
    layout->addWidget(new QLabel("计数:"), 3, 0);
    m_countSpin = new QSpinBox();
    m_countSpin->setObjectName("countSpin");
    m_countSpin->setRange(0, 100);
    m_countSpin->setValue(10);
    layout->addWidget(m_countSpin, 3, 1);
    
    // 数值
    layout->addWidget(new QLabel("数值:"), 4, 0);
    m_valueSpin = new QDoubleSpinBox();
    m_valueSpin->setObjectName("valueSpin");
    m_valueSpin->setRange(0.0, 10.0);
    m_valueSpin->setDecimals(2);
    m_valueSpin->setValue(5.0);
    layout->addWidget(m_valueSpin, 4, 1);
    
    // 进度滑块
    layout->addWidget(new QLabel("进度:"), 5, 0);
    m_progressSlider = new QSlider(Qt::Horizontal);
    m_progressSlider->setObjectName("progressSlider");
    m_progressSlider->setRange(0, 100);
    m_progressSlider->setValue(50);
    layout->addWidget(m_progressSlider, 5, 1);
    
    return group;
}

QGroupBox* SimpleExample::createButtonGroup()
{
    QGroupBox* group = new QGroupBox("操作");
    QHBoxLayout* layout = new QHBoxLayout(group);
    
    m_showTableButton = new QPushButton("显示控件表");
    m_updateButton = new QPushButton("模拟更新");
    m_resetButton = new QPushButton("重置控件");
    
    layout->addWidget(m_showTableButton);
    layout->addWidget(m_updateButton);
    layout->addWidget(m_resetButton);
    
    // 连接信号
    connect(m_showTableButton, &QPushButton::clicked, this, &SimpleExample::showControlTable);
    connect(m_updateButton, &QPushButton::clicked, this, &SimpleExample::simulateExternalUpdate);
    connect(m_resetButton, &QPushButton::clicked, this, &SimpleExample::resetAllControls);
    
    return group;
}

QGroupBox* SimpleExample::createLogGroup()
{
    QGroupBox* group = new QGroupBox("日志");
    QVBoxLayout* layout = new QVBoxLayout(group);
    
    m_logDisplay = new QTextEdit();
    m_logDisplay->setMaximumHeight(200);
    m_logDisplay->setReadOnly(true);
    layout->addWidget(m_logDisplay);
    
    return group;
}

void SimpleExample::initializeBinding()
{
    // 创建数据绑定器
    m_binder = new SimpleUIBinder(this, this);
    
    // 连接信号
    connect(m_binder, &SimpleUIBinder::initialized, 
            this, &SimpleExample::onBindingInitialized);
    connect(m_binder, &SimpleUIBinder::dataChanged,
            this, &SimpleExample::onDataChanged);
    
    // 初始化绑定
    m_binder->initialize();
}

void SimpleExample::onBindingInitialized(int controlCount, const QList<SimpleControlInfo>& controls)
{
    m_logDisplay->append(QString("=== 绑定初始化完成 ==="));
    m_logDisplay->append(QString("总共绑定了 %1 个控件:").arg(controlCount));
    
    for (const SimpleControlInfo& info : controls) {
        m_logDisplay->append(QString("[%1] %2 (%3) = %4")
                            .arg(info.index)
                            .arg(info.name)
                            .arg(info.type)
                            .arg(info.value.toString()));
    }
    m_logDisplay->append("========================");
}

void SimpleExample::onDataChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue)
{
    m_logDisplay->append(QString("数据变化: [%1] %2: %3 -> %4")
                        .arg(index)
                        .arg(name)
                        .arg(oldValue.toString())
                        .arg(value.toString()));
    
    // 滚动到底部
    m_logDisplay->moveCursor(QTextCursor::End);
}

void SimpleExample::showControlTable()
{
    QList<SimpleControlInfo> controls = m_binder->getAllControls();
    
    QString tableText = "控件序号表:\n\n";
    tableText += "序号\t控件名\t\t类型\t\t当前值\n";
    tableText += "----\t------\t\t----\t\t------\n";
    
    for (const SimpleControlInfo& info : controls) {
        tableText += QString("%1\t%2\t\t%3\t%4\n")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
    
    QMessageBox::information(this, "控件序号表", tableText);
}

void SimpleExample::simulateExternalUpdate()
{
    m_updateCounter++;
    
    // 通过序号更新控件值
    m_binder->updateValue(0, QString("外部更新 #%1").arg(m_updateCounter)); // nameEdit
    m_binder->updateValue(1, m_updateCounter % 3); // typeCombo
    m_binder->updateValue(2, m_updateCounter % 2 == 0); // enabledCheck
    m_binder->updateValue(3, (m_updateCounter * 7) % 101); // countSpin
    m_binder->updateValue(4, (m_updateCounter * 1.5)); // valueSpin
    m_binder->updateValue(5, (m_updateCounter * 13) % 101); // progressSlider
    
    m_logDisplay->append(QString("=== 执行外部更新 #%1 ===").arg(m_updateCounter));
}

void SimpleExample::resetAllControls()
{
    // 重置为初始值
    m_binder->updateValueByName("nameEdit", "默认名称");
    m_binder->updateValueByName("typeCombo", 0);
    m_binder->updateValueByName("enabledCheck", true);
    m_binder->updateValueByName("countSpin", 10);
    m_binder->updateValueByName("valueSpin", 5.0);
    m_binder->updateValueByName("progressSlider", 50);
    
    m_logDisplay->append("=== 重置所有控件 ===");
}

void SimpleExample::updateSimulatedData()
{
    // 自动模拟数据更新
    static int autoCounter = 0;
    autoCounter++;
    
    // 随机更新一个控件
    int controlIndex = autoCounter % m_binder->getControlCount();
    
    switch (controlIndex) {
    case 0: // nameEdit
        m_binder->updateValue(0, QString("自动更新 %1").arg(autoCounter));
        break;
    case 1: // typeCombo
        m_binder->updateValue(1, autoCounter % 3);
        break;
    case 2: // enabledCheck
        m_binder->updateValue(2, autoCounter % 2 == 0);
        break;
    case 3: // countSpin
        m_binder->updateValue(3, (autoCounter * 3) % 101);
        break;
    case 4: // valueSpin
        m_binder->updateValue(4, (autoCounter * 0.7));
        break;
    case 5: // progressSlider
        m_binder->updateValue(5, (autoCounter * 11) % 101);
        break;
    }
}
