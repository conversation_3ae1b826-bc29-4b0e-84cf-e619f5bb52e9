#include "inherited_example.h"
#include <QDebug>
#include <QTime>

InheritedExample::InheritedExample(QWidget *parent)
    : BindableWidget(parent)  // 继承BindableWidget而不是QWidget
    , m_nameEdit(nullptr)
    , m_type<PERSON>ombo(nullptr)
    , m_enabledCheck(nullptr)
    , m_countSpin(nullptr)
    , m_valueSpin(nullptr)
    , m_progressSlider(nullptr)
    , m_logDisplay(nullptr)
    , m_autoUpdateTimer(nullptr)
    , m_updateCounter(0)
{
    setWindowTitle("基于继承的数据绑定示例");
    setMinimumSize(700, 600);
    
    // 创建UI界面
    createUI();
    
    // 初始化数据绑定（这是关键步骤！）
    initializeBinding();
    
    // 创建定时器
    m_autoUpdateTimer = new QTimer(this);
    connect(m_autoUpdateTimer, &QTimer::timeout, this, &InheritedExample::autoUpdate);
    m_autoUpdateTimer->start(4000); // 每4秒自动更新一次
    
    addLog("=== 基于继承的数据绑定示例启动 ===");
    addLog("所有控件已自动绑定，数据变化会自动通知外部");
}

InheritedExample::~InheritedExample()
{
    // 基类析构函数会自动处理绑定清理
}

void InheritedExample::createUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    mainLayout->addWidget(createControlGroup());
    mainLayout->addWidget(createButtonGroup());
    mainLayout->addWidget(createLogGroup());
    
    setLayout(mainLayout);
}

QGroupBox* InheritedExample::createControlGroup()
{
    QGroupBox* group = new QGroupBox("自动绑定的控件");
    QGridLayout* layout = new QGridLayout(group);
    
    // 名称输入
    layout->addWidget(new QLabel("名称:"), 0, 0);
    m_nameEdit = new QLineEdit();
    m_nameEdit->setObjectName("nameEdit");  // 设置objectName是绑定的关键！
    m_nameEdit->setText("继承示例");
    layout->addWidget(m_nameEdit, 0, 1);
    
    // 类型选择
    layout->addWidget(new QLabel("类型:"), 1, 0);
    m_typeCombo = new QComboBox();
    m_typeCombo->setObjectName("typeCombo");
    m_typeCombo->addItems({"继承模式", "组合模式", "混合模式"});
    layout->addWidget(m_typeCombo, 1, 1);
    
    // 启用状态
    m_enabledCheck = new QCheckBox("自动绑定启用");
    m_enabledCheck->setObjectName("enabledCheck");
    m_enabledCheck->setChecked(true);
    layout->addWidget(m_enabledCheck, 2, 0, 1, 2);
    
    // 计数器
    layout->addWidget(new QLabel("计数:"), 3, 0);
    m_countSpin = new QSpinBox();
    m_countSpin->setObjectName("countSpin");
    m_countSpin->setRange(0, 200);
    m_countSpin->setValue(20);
    layout->addWidget(m_countSpin, 3, 1);
    
    // 数值
    layout->addWidget(new QLabel("数值:"), 4, 0);
    m_valueSpin = new QDoubleSpinBox();
    m_valueSpin->setObjectName("valueSpin");
    m_valueSpin->setRange(0.0, 100.0);
    m_valueSpin->setDecimals(2);
    m_valueSpin->setValue(15.75);
    layout->addWidget(m_valueSpin, 4, 1);
    
    // 进度滑块
    layout->addWidget(new QLabel("进度:"), 5, 0);
    m_progressSlider = new QSlider(Qt::Horizontal);
    m_progressSlider->setObjectName("progressSlider");
    m_progressSlider->setRange(0, 100);
    m_progressSlider->setValue(75);
    layout->addWidget(m_progressSlider, 5, 1);
    
    return group;
}

QGroupBox* InheritedExample::createButtonGroup()
{
    QGroupBox* group = new QGroupBox("操作按钮");
    QHBoxLayout* layout = new QHBoxLayout(group);
    
    m_showTableButton = new QPushButton("显示控件表");
    m_updateButton = new QPushButton("模拟外部更新");
    m_resetButton = new QPushButton("重置控件");
    m_batchUpdateButton = new QPushButton("批量更新");
    
    // 注意：按钮不设置objectName，所以不会被自动绑定
    // 我们只关心它们的点击事件，不关心它们的"值"
    
    layout->addWidget(m_showTableButton);
    layout->addWidget(m_updateButton);
    layout->addWidget(m_resetButton);
    layout->addWidget(m_batchUpdateButton);
    
    // 连接按钮信号
    connect(m_showTableButton, &QPushButton::clicked, this, &InheritedExample::showControlTable);
    connect(m_updateButton, &QPushButton::clicked, this, &InheritedExample::simulateExternalUpdate);
    connect(m_resetButton, &QPushButton::clicked, this, &InheritedExample::resetAllControls);
    connect(m_batchUpdateButton, &QPushButton::clicked, this, &InheritedExample::batchUpdateControls);
    
    return group;
}

QGroupBox* InheritedExample::createLogGroup()
{
    QGroupBox* group = new QGroupBox("数据变化日志");
    QVBoxLayout* layout = new QVBoxLayout(group);
    
    m_logDisplay = new QTextEdit();
    m_logDisplay->setMaximumHeight(200);
    m_logDisplay->setReadOnly(true);
    layout->addWidget(m_logDisplay);
    
    return group;
}

void InheritedExample::addLog(const QString& message)
{
    m_logDisplay->append(QString("[%1] %2")
                        .arg(QTime::currentTime().toString("hh:mm:ss"))
                        .arg(message));
    m_logDisplay->moveCursor(QTextCursor::End);
}

void InheritedExample::onDataChanged(int index, const QString& name, 
                                    const QVariant& newValue, const QVariant& oldValue)
{
    // 重写基类的虚函数，处理数据变化
    addLog(QString("控件数据变化: [%1] %2: %3 -> %4")
           .arg(index)
           .arg(name)
           .arg(oldValue.toString())
           .arg(newValue.toString()));
    
    // 模拟处理数据变化（比如发送给外部系统）
    processDataChange(index, name, newValue);
    
    // 也可以发射自定义信号通知其他组件
    emit controlDataChanged(index, name, newValue, oldValue);
}

void InheritedExample::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    // 重写基类的虚函数，处理绑定初始化完成
    addLog(QString("=== 自动绑定初始化完成 ==="));
    addLog(QString("总共自动绑定了 %1 个控件:").arg(controlCount));
    
    // 建立控件名到序号的映射，方便后续使用
    m_controlIndexMap.clear();
    for (const ControlBindInfo& info : controls) {
        m_controlIndexMap[info.name] = info.index;
        addLog(QString("  [%1] %2 (%3) = %4")
               .arg(info.index)
               .arg(info.name)
               .arg(info.type)
               .arg(info.value.toString()));
    }
    addLog("========================");
}

void InheritedExample::processDataChange(int index, const QString& name, const QVariant& value)
{
    // 模拟外部系统处理数据变化
    if (name == "enabledCheck") {
        bool enabled = value.toBool();
        addLog(QString("外部系统: 自动绑定已%1").arg(enabled ? "启用" : "禁用"));
    }
    else if (name == "countSpin") {
        int count = value.toInt();
        if (count > 150) {
            addLog("外部系统: 警告 - 计数值过高！");
        }
    }
    else if (name == "progressSlider") {
        int progress = value.toInt();
        if (progress >= 100) {
            addLog("外部系统: 进度已完成！");
        }
    }
    
    // 这里可以添加更多的业务逻辑，比如：
    // - 发送数据到网络
    // - 保存到数据库
    // - 更新其他界面
    // - 触发其他操作
}

void InheritedExample::showControlTable()
{
    QList<ControlBindInfo> controls = getAllControls();
    
    QString tableText = "自动绑定的控件表:\n\n";
    tableText += "序号\t控件名\t\t类型\t\t当前值\n";
    tableText += "----\t------\t\t----\t\t------\n";
    
    for (const ControlBindInfo& info : controls) {
        tableText += QString("%1\t%2\t\t%3\t%4\n")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
    
    QMessageBox::information(this, "自动绑定控件表", tableText);
}

void InheritedExample::simulateExternalUpdate()
{
    m_updateCounter++;
    
    // 通过序号更新控件值
    updateControlValue(0, QString("外部更新 #%1").arg(m_updateCounter)); // nameEdit
    updateControlValue(1, m_updateCounter % 3); // typeCombo
    updateControlValue(2, m_updateCounter % 2 == 0); // enabledCheck
    updateControlValue(3, (m_updateCounter * 11) % 201); // countSpin
    updateControlValue(4, (m_updateCounter * 2.5)); // valueSpin
    updateControlValue(5, (m_updateCounter * 17) % 101); // progressSlider
    
    addLog(QString("=== 执行外部更新 #%1 ===").arg(m_updateCounter));
}

void InheritedExample::resetAllControls()
{
    // 通过控件名重置为初始值
    updateControlValueByName("nameEdit", "继承示例");
    updateControlValueByName("typeCombo", 0);
    updateControlValueByName("enabledCheck", true);
    updateControlValueByName("countSpin", 20);
    updateControlValueByName("valueSpin", 15.75);
    updateControlValueByName("progressSlider", 75);
    
    addLog("=== 重置所有控件为初始值 ===");
}

void InheritedExample::batchUpdateControls()
{
    // 批量更新多个控件
    QMap<int, QVariant> updates;
    updates[0] = "批量更新测试";
    updates[1] = 2;
    updates[2] = false;
    updates[3] = 99;
    updates[4] = 88.88;
    updates[5] = 88;
    
    int successCount = updateMultipleValues(updates);
    addLog(QString("=== 批量更新完成，成功更新 %1 个控件 ===").arg(successCount));
}

void InheritedExample::autoUpdate()
{
    // 自动更新一个随机控件
    static int autoCounter = 0;
    autoCounter++;
    
    int controlCount = getControlCount();
    if (controlCount == 0) return;
    
    int randomIndex = autoCounter % controlCount;
    ControlBindInfo info = getControlInfo(randomIndex);
    
    QVariant newValue;
    if (info.type.contains("LineEdit")) {
        newValue = QString("自动更新 %1").arg(autoCounter);
    } else if (info.type.contains("ComboBox")) {
        newValue = autoCounter % 3;
    } else if (info.type.contains("CheckBox")) {
        newValue = (autoCounter % 2 == 0);
    } else if (info.type.contains("SpinBox")) {
        newValue = (autoCounter * 7) % 201;
    } else if (info.type.contains("Slider")) {
        newValue = (autoCounter * 13) % 101;
    }
    
    if (newValue.isValid()) {
        updateControlValue(randomIndex, newValue);
        addLog(QString("自动更新: [%1] %2 = %3").arg(randomIndex).arg(info.name).arg(newValue.toString()));
    }
}

void InheritedExample::handleExternalSignal(int index, const QString& name, const QVariant& value)
{
    // 处理外部信号的示例函数
    addLog(QString("收到外部信号: [%1] %2 = %3").arg(index).arg(name).arg(value.toString()));
    
    // 可以根据外部信号更新控件
    updateControlValue(index, value);
}
