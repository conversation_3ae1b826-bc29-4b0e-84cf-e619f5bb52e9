<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GripperProcess</class>
 <widget class="QWidget" name="GripperProcess">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>866</width>
    <height>734</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(240, 248, 255, 255),
        stop:1 rgba(230, 240, 250, 255));
font: 16px &quot;Microsoft YaHei&quot;;
}

QGroupBox {
    font-weight: bold;
    border: 2px solid rgba(100, 150, 200, 150);
    border-radius: 8px;
    margin-top: 10px;
    background: rgba(255, 255, 255, 100);
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: rgb(1, 7, 126);
}
QGroupBox QLabel{background-color: transparent;}

QWidget {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(240, 248, 255, 255),
        stop:1 rgba(230, 240, 250, 255));
font: 16px &quot;Microsoft YaHei&quot;;
}

QPushButton {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(80, 160, 240, 255),   /* 提亮蓝起点 */
        stop:1 rgba(60, 140, 220, 255));  /* 提亮蓝终点 */
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: white;
    padding: 10px;
    font: 18px &quot;Microsoft YaHei&quot;;
    font-weight: bold;
    border-radius: 10px;
    min-height: 28px;
}

QPushButton:hover {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(100, 175, 250, 255),
        stop:1 rgba(80, 160, 240, 255));
}

QPushButton:pressed {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(50, 130, 210, 255),
        stop:1 rgba(40, 110, 190, 255));
}


/* tabBar样式设置*/
QTabWidget::pane {
background: #ffffff;
border: none;
border-top: 2px solid #005bac;
}
QTabWidget::tab-bar {
alignment: left;
}
QTabBar::tab{
height: 40px;
width:100px;
background: #ededed;
border: 1px solid;
border-color: #cdcdcd;
border-bottom: none;
font-family: Microsoft YaHei;
color: #595959;
font-size: 18px;
line-height: 20px;
}
QTabBar::tab:selected {
border-color: #b8b8b8;
background : #005bac;
font-family: Microsoft YaHei;
color: #ffffff;
font-size: 18px;
line-height: 20px;
}
QTabBar::close-button {
image: url(:/image/close_12px.png);
subcontrol-position: right;
}
QTabBar::close-button:hover {
image: url(:/image/close_12px_w.png);
}
QTabBar::close-button:selected {
image: url(:/image/close_12px_w.png);
}

QComboBox {
    background-color: #F0F8FF; /* 淡雅的浅蓝色，与背景协调 */
    color: #1F354D;             /* 深蓝灰，易读 */
    border: 1px solid #A4B4C8;  /* 柔和边框色 */
    border-radius: 4px;
    padding: 6px 12px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #14C9E2;  /* 亮蓝高亮色 */
}

QComboBox:focus {
    border-color: #005BAC;  /* 深蓝聚焦色 */
}

QComboBox::drop-down {
    background-color: #E6F0FA;
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
        image: url(:/img/img/down_arrow_hd.png);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #F7FBFF;      /* 很浅的蓝白色 */
    color: #1F354D;                 /* 保持可读性 */
    border: 1px solid #A4B4C8;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #BBDFFF; /* 柔和的选中背景 */
    selection-color: #0A2647;            /* 深蓝选中文字 */
}


QSpinBox, QDoubleSpinBox {
    background-color: #F0F8FF;           /* 与背景接近但稍微深一点 */
    color: #1F354D;                      /* 深蓝灰字体 */
    border: 1px solid #A4B4C8;           /* 柔和边框 */
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 24px;
    font-weight: 500;
}

/* 鼠标悬停时边框颜色高亮 */
QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #14C9E2;
}

/* 焦点状态边框加深 */
QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #005BAC;
}

/* 按钮区域样式（上下箭头） */
QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #E6F0FA;
    border: none;
    width: 16px;
    padding: 2px;
    subcontrol-origin: border;
}

/* 鼠标悬停按钮时颜色稍亮 */
QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #D0E4F5;
}

/* 按钮按下状态颜色加深 */
QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #BBDFFF;
}

/* 禁用状态 */
QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #D6E5F2;
    color: #9BAEC3;
    border-color: #B0C4D8;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow{
        width: 10px;
    height: 10px;
        image: url(:/img/img/up_arrow_hd.png);
}
QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    width: 10px;
    height: 10px;
        image: url(:/img/img/down_arrow_hd.png);
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    transform: rotate(180deg); /* 向下 */
}

/*****************************************************************************
 * QTableWidget 本体
 *****************************************************************************/
QTableWidget {
    /* 外边框与网格线 */
    border: 1px solid #B0C4DE;          /* 淡钢蓝外框 */
    gridline-color: #DDE6F0;            /* 更浅的网格线 */

    /* 交替行 */
    alternate-background-color: rgba(255,255,255,180);   /* 必须同时在代码里 setAlternatingRowColors(true) */

    /* 选中高亮 */
    selection-background-color: rgba(100,149,237,120);   /* 淡群青 */
    /*selection-color: #FFFFFF;                            /* 选中文字为白色 */
        selection-color: #1C1C1C;  /* 深灰黑，更清晰 */
}

/*****************************************************************************
 * 表头：水平 &amp; 垂直（QHeaderView）
 *****************************************************************************/
QHeaderView::section {
    background: qlineargradient(spread:pad,
                                x1:0, y1:0, x2:0, y2:1,
                                stop:0 rgba(225,235,245,255),
                                stop:1 rgba(205,220,235,255));
    padding: 4px 8px;
    border: 1px solid #B0C4DE;
    color: #2F4F4F;             /* 深灰文字 */
    font-weight: bold;
}

/* 鼠标悬停表头时微亮一点 */
QHeaderView::section:hover {
    background: qlineargradient(spread:pad,
                                x1:0, y1:0, x2:0, y2:1,
                                stop:0 rgba(235,245,255,255),
                                stop:1 rgba(215,230,245,255));
}

/* 排序箭头 */
QHeaderView::up-arrow    { image: url(:/icons/arrow_up.svg);   width:10px; height:10px; }
QHeaderView::down-arrow  { image: url(:/icons/arrow_down.svg); width:10px; height:10px; }

/*****************************************************************************
 * 左上角空白的 corner button
 *****************************************************************************/
QTableCornerButton::section {
    background: qlineargradient(spread:pad,
                                x1:0, y1:0, x2:0, y2:1,
                                stop:0 rgba(225,235,245,255),
                                stop:1 rgba(205,220,235,255));
    border: 1px solid #B0C4DE;
}

/*****************************************************************************
 * 单元格交互
 *****************************************************************************/
QTableWidget::item:hover {
    background: rgba(135,206,250,120);          /* 悬停淡天蓝 */
}

QTableWidget::item:selected:hover {
    background: rgba(70,130,180,180);           /* 选中 + 悬停时稍深化 */
}

/*****************************************************************************
 * 滚动条：简洁细条风格（可选）
 *****************************************************************************/
QScrollBar:vertical {
    width: 8px;
    background: transparent;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: rgba(120,170,220,150);
    min-height: 20px;
    border-radius: 4px;
}

QScrollBar::handle:vertical:hover {
    background: rgba(70,130,180,180);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;     /* 去掉上下小按钮 */
}

QScrollBar:horizontal {
    height: 8px;
    background: transparent;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: rgba(120,170,220,150);
    min-width: 20px;
    border-radius: 4px;
}

QScrollBar::handle:horizontal:hover {
    background: rgba(70,130,180,180);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;      /* 去掉左右小按钮 */
}

QGroupBox {
    font-weight: bold;
    border: 2px solid rgba(100, 150, 200, 150);
    border-radius: 8px;
    margin-top: 10px;
    background: rgba(255, 255, 255, 100);
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: rgb(1, 7, 126);
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QWidget" name="headerWidget" native="true">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>80</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget {
    background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(255, 255, 255, 200),
        stop:1 rgba(245, 250, 255, 200));
    border: 1px solid rgba(200, 220, 240, 150);
    border-radius: 10px;
}</string>
     </property>
     <layout class="QHBoxLayout" name="headerLayout_2">
      <item>
       <widget class="QLabel" name="titleLabel">
        <property name="font">
         <font>
          <family>Microsoft YaHei</family>
          <pointsize>-1</pointsize>
          <weight>75</weight>
          <italic>false</italic>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">
QLabel {
    background: transparent;
        border:none;
        color: rgb(1, 7, 126);
        font: 45px &quot;Microsoft YaHei&quot;;
        font-weight: bold;
}</string>
        </property>
        <property name="text">
         <string>ADA夹爪功能头控制系统</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="headerSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="styleSheet">
         <string notr="true">QLabel{
    /*background-color: #E6F0FA;    /* 浅蓝背景，避免像按钮 */
    color: #003366;               /* 深蓝字体 */
    /*border: 1px solid #A9C6EA;    /* 柔和边框 */

    /*border-radius: 0px;*/
        background: transparent;
        border:none;
    padding: 6px 10px;
    font-size: 14px;
    font-weight: bold;            /* 字体加粗 */
}</string>
        </property>
        <property name="text">
         <string>版本号：V1.0.1</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,0">
     <item>
      <widget class="QGroupBox" name="deviceImageGroup">
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    font-weight: bold;
    border: 2px solid rgba(100, 150, 200, 150);
    border-radius: 8px;
    margin-top: 10px;
    background: rgba(255, 255, 255, 100);
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: rgb(1, 7, 126);
}</string>
       </property>
       <property name="title">
        <string>设备3D视图</string>
       </property>
       <layout class="QGridLayout" name="deviceImageLayout">
        <property name="leftMargin">
         <number>11</number>
        </property>
        <property name="topMargin">
         <number>11</number>
        </property>
        <property name="rightMargin">
         <number>11</number>
        </property>
        <property name="bottomMargin">
         <number>11</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QFrame" name="widget_LogInfo">
          <property name="styleSheet">
           <string notr="true">background-color: rgb(255, 255, 255);
border: 1px solid rgba(200, 200, 200, 100);
border-radius: 5px;</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="panel" native="true">
       <layout class="QVBoxLayout" name="rightLayout">
        <item>
         <widget class="QGroupBox" name="deviceInfoGroup">
          <property name="styleSheet">
           <string notr="true">QGroupBox {
    font-weight: bold;
    border: 2px solid rgba(100, 150, 200, 150);
    border-radius: 8px;
    margin-top: 10px;
    background: rgba(255, 255, 255, 100);
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: rgb(1, 7, 126);
}</string>
          </property>
          <property name="title">
           <string>设备信息</string>
          </property>
          <layout class="QGridLayout" name="deviceInfoLayout">
           <item row="0" column="0">
            <widget class="QLabel" name="label_3">
             <property name="styleSheet">
              <string notr="true"/>
             </property>
             <property name="text">
              <string>模组编号：</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_8">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(0, 100, 0);</string>
             </property>
             <property name="text">
              <string>N888999</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>模组类型：</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLabel" name="label_9">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(0, 100, 0);</string>
             </property>
             <property name="text">
              <string>精密夹爪模组</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>控制器状态：</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="label_12">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(200, 0, 0);</string>
             </property>
             <property name="text">
              <string>未连接</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="label_13">
             <property name="text">
              <string>相机状态：</string>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLabel" name="label_14">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(200, 0, 0);</string>
             </property>
             <property name="text">
              <string>未连接</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>剩余寿命：</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="label_11">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(0, 100, 0);</string>
             </property>
             <property name="text">
              <string>99999</string>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>累计工作量(次)：</string>
             </property>
            </widget>
           </item>
           <item row="2" column="3">
            <widget class="QLabel" name="label_10">
             <property name="styleSheet">
              <string notr="true">font-weight: bold; color: rgb(0, 100, 0);</string>
             </property>
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="statusSection">
          <property name="title">
           <string>设备状态监控</string>
          </property>
          <layout class="QGridLayout" name="statusGrid">
           <item row="0" column="0">
            <widget class="QLabel" name="status1">
             <property name="text">
              <string>工作状态: 待命中</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="status2">
             <property name="text">
              <string>夹爪状态: 张开</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="status3">
             <property name="text">
              <string>开合距离:15.0mm</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLabel" name="status4">
             <property name="text">
              <string>物品检测: 无</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="status5">
             <property name="text">
              <string>夹持力反馈</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="status6">
             <property name="text">
              <string>当前夹持力：0.0N</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="status7">
             <property name="text">
              <string>最大夹持力:50.0N</string>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLabel" name="status8">
             <property name="text">
              <string>夹爪温度: 26摄氏度</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="controlSection">
          <property name="title">
           <string>快捷面板</string>
          </property>
          <layout class="QGridLayout" name="controlGrid">
           <item row="1" column="2">
            <widget class="QPushButton" name="btnEmergencyStop">
             <property name="text">
              <string>强力夹持</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QPushButton" name="btnTorqueCalib">
             <property name="text">
              <string>紧急释放</string>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QPushButton" name="btnDepthCalib">
             <property name="text">
              <string>力度校准</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QPushButton" name="btnDualAxis">
             <property name="text">
              <string>夹爪闭合</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QPushButton" name="btnSingleAxis">
             <property name="text">
              <string>夹爪张开</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QPushButton" name="btnZ2Zero">
             <property name="text">
              <string>标准夹持</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QPushButton" name="btnSystemReset">
             <property name="text">
              <string>夹爪归零</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QPushButton" name="btnScrewDetect">
             <property name="text">
              <string>物品检测</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="btnZ1Zero">
             <property name="text">
              <string>轻柔夹持</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="logSection">
          <property name="title">
           <string>实时信息</string>
          </property>
          <layout class="QVBoxLayout" name="logLayout">
           <item>
            <widget class="QTextEdit" name="logTextEdit"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
