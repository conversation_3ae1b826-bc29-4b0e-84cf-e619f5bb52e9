#ifndef SIMPLE_UI_BINDER_H
#define SIMPLE_UI_BINDER_H

#include <QObject>
#include <QWidget>
#include <QVariant>
#include <QMap>
#include <QList>
#include <QMetaObject>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QRadioButton>

/**
 * @brief 简单的控件信息结构
 */
struct SimpleControlInfo {
    int index;              // 控件序号
    QWidget* widget;        // 控件指针
    QString name;           // 控件名称(objectName)
    QString type;           // 控件类型
    QVariant value;         // 当前值
    
    SimpleControlInfo() : index(-1), widget(nullptr) {}
};

/**
 * @brief 简单UI数据绑定器
 * 
 * 核心功能：
 * 1. 自动扫描界面控件并生成带序号的控件表
 * 2. 初始化完成后发送信号给外部
 * 3. 控件数据变化时发送信号给外部
 * 4. 支持外部通过序号更新控件数据
 */
class SimpleUIBinder : public QObject
{
    Q_OBJECT

public:
    explicit SimpleUIBinder(QWidget* parentWidget, QObject *parent = nullptr);
    ~SimpleUIBinder();

    /**
     * @brief 初始化绑定 - 扫描所有控件并建立绑定
     * @return 绑定的控件数量
     */
    int initialize();

    /**
     * @brief 通过序号更新控件值
     * @param index 控件序号
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateValue(int index, const QVariant& value);

    /**
     * @brief 通过控件名更新控件值
     * @param name 控件名称
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateValueByName(const QString& name, const QVariant& value);

    /**
     * @brief 获取控件当前值
     * @param index 控件序号
     * @return 当前值
     */
    QVariant getValue(int index) const;

    /**
     * @brief 获取控件信息
     * @param index 控件序号
     * @return 控件信息
     */
    SimpleControlInfo getControlInfo(int index) const;

    /**
     * @brief 获取所有控件信息列表
     * @return 控件信息列表
     */
    QList<SimpleControlInfo> getAllControls() const;

    /**
     * @brief 获取控件总数
     * @return 控件数量
     */
    int getControlCount() const { return m_controls.size(); }

    /**
     * @brief 根据控件名查找序号
     * @param name 控件名称
     * @return 控件序号，未找到返回-1
     */
    int findIndexByName(const QString& name) const;

signals:
    /**
     * @brief 初始化完成信号
     * @param controlCount 绑定的控件数量
     * @param controls 所有控件信息列表
     */
    void initialized(int controlCount, const QList<SimpleControlInfo>& controls);

    /**
     * @brief 控件数据变化信号
     * @param index 控件序号
     * @param name 控件名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void dataChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue);

private slots:
    /**
     * @brief 处理控件值变化的统一槽函数
     */
    void onControlValueChanged();

private:
    /**
     * @brief 递归扫描控件
     * @param widget 要扫描的控件
     */
    void scanWidget(QWidget* widget);

    /**
     * @brief 检查控件是否支持绑定
     * @param widget 控件指针
     * @return 是否支持
     */
    bool isSupportedWidget(QWidget* widget) const;

    /**
     * @brief 获取控件类型名称
     * @param widget 控件指针
     * @return 类型名称
     */
    QString getWidgetTypeName(QWidget* widget) const;

    /**
     * @brief 连接控件信号
     * @param info 控件信息
     * @return 是否连接成功
     */
    bool connectWidgetSignal(SimpleControlInfo& info);

    /**
     * @brief 断开控件信号连接
     * @param info 控件信息
     */
    void disconnectWidgetSignal(SimpleControlInfo& info);

    /**
     * @brief 获取控件当前值
     * @param widget 控件指针
     * @return 当前值
     */
    QVariant getWidgetValue(QWidget* widget) const;

    /**
     * @brief 设置控件值
     * @param widget 控件指针
     * @param value 要设置的值
     * @return 是否设置成功
     */
    bool setWidgetValue(QWidget* widget, const QVariant& value);

private:
    QWidget* m_parentWidget;                    // 父控件
    QList<SimpleControlInfo> m_controls;        // 控件信息列表（按序号排序）
    QMap<QString, int> m_nameToIndex;           // 控件名到序号的映射
    QMap<QWidget*, int> m_widgetToIndex;        // 控件指针到序号的映射
    bool m_initialized;                         // 是否已初始化
};

#endif // SIMPLE_UI_BINDER_H
