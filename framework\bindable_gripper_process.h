#ifndef BINDABLE_GRIPPER_PROCESS_H
#define BINDABLE_GRIPPER_PROCESS_H

#include <QTimer>
#include <QDebug>
#include <QJsonObject>
#include <QJsonDocument>
#include <QPushButton>
#include "bindable_widget.h"
#include "../gripperprocess.h"

/**
 * @brief 可绑定的夹爪工艺界面
 * 
 * 演示如何将现有的Qt Designer界面改造为继承BindableWidget的模式：
 * 1. 继承BindableWidget而不是QWidget
 * 2. 包含原始的GripperProcess作为成员
 * 3. 自动绑定所有控件
 * 4. 提供数据变化通知和外部控制接口
 */
class BindableGripperProcess : public BindableWidget
{
    Q_OBJECT

public:
    /**
     * @brief 夹爪状态枚举
     */
    enum GripperState {
        Idle = 0,           // 空闲
        Opening = 1,        // 张开中
        Closing = 2,        // 闭合中
        Gripping = 3,       // 夹持中
        Error = 4           // 错误状态
    };

    explicit BindableGripperProcess(QWidget *parent = nullptr);
    ~BindableGripperProcess();

    /**
     * @brief 获取当前夹爪状态
     * @return 夹爪状态
     */
    GripperState getCurrentState() const { return m_currentState; }

    /**
     * @brief 获取所有参数数据
     * @return 参数数据映射
     */
    QMap<int, QVariant> getAllParameterData() const;

    /**
     * @brief 导出配置为JSON
     * @return JSON配置对象
     */
    QJsonObject exportConfiguration() const;

    /**
     * @brief 导入JSON配置
     * @param config JSON配置对象
     * @return 是否导入成功
     */
    bool importConfiguration(const QJsonObject& config);

protected:
    /**
     * @brief 重写数据变化处理函数
     * 当任何绑定的控件数据发生变化时会自动调用
     */
    void onDataChanged(int index, const QString& name, 
                      const QVariant& newValue, const QVariant& oldValue) override;

    /**
     * @brief 重写绑定初始化完成处理函数
     * 当绑定初始化完成时会自动调用
     */
    void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls) override;

public slots:
    /**
     * @brief 更新夹爪状态
     * @param state 新状态
     */
    void updateGripperState(GripperState state);

    /**
     * @brief 执行夹爪操作
     * @param operation 操作类型
     * @param parameters 操作参数
     */
    void executeGripperOperation(const QString& operation, const QVariant& parameters = QVariant());

    /**
     * @brief 重置所有参数
     */
    void resetAllParameters();

    /**
     * @brief 模拟夹爪数据更新
     */
    void simulateGripperData();

signals:
    /**
     * @brief 夹爪参数发生变化
     * @param index 参数索引
     * @param name 参数名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void gripperParameterChanged(int index, const QString& name, 
                                const QVariant& value, const QVariant& oldValue);

    /**
     * @brief 夹爪状态发生变化
     * @param newState 新状态
     * @param oldState 旧状态
     */
    void gripperStateChanged(GripperState newState, GripperState oldState);

    /**
     * @brief 夹爪操作请求
     * @param operation 操作类型
     * @param parameters 操作参数
     */
    void gripperOperationRequested(const QString& operation, const QVariant& parameters);

    /**
     * @brief 配置发生变化
     * @param config 新配置
     */
    void configurationChanged(const QJsonObject& config);

    /**
     * @brief 参数验证失败
     * @param parameterName 参数名称
     * @param value 无效值
     * @param errorMessage 错误信息
     */
    void parameterValidationFailed(const QString& parameterName, 
                                  const QVariant& value, const QString& errorMessage);

private slots:
    /**
     * @brief 处理按钮点击事件
     */
    void onButtonClicked();

    /**
     * @brief 定时更新模拟数据
     */
    void updateSimulatedData();

private:
    /**
     * @brief 初始化界面
     */
    void initializeInterface();

    /**
     * @brief 设置控件名称（如果需要的话）
     */
    void setupControlNames();

    /**
     * @brief 连接按钮信号
     */
    void connectButtonSignals();

    /**
     * @brief 验证参数值
     * @param name 参数名称
     * @param value 参数值
     * @return 是否有效
     */
    bool validateParameter(const QString& name, const QVariant& value);

    /**
     * @brief 处理特定的夹爪操作
     * @param buttonName 按钮名称
     */
    void handleGripperOperation(const QString& buttonName);

    /**
     * @brief 状态枚举转字符串
     * @param state 状态枚举
     * @return 状态字符串
     */
    QString stateToString(GripperState state) const;

    /**
     * @brief 更新状态显示
     */
    void updateStatusDisplay();

private:
    GripperProcess* m_gripperProcess;           // 原始的夹爪界面
    GripperState m_currentState;                // 当前夹爪状态
    QTimer* m_simulationTimer;                  // 模拟数据定时器
    QMap<QString, QString> m_buttonOperationMap; // 按钮名到操作的映射
    QMap<QString, int> m_parameterIndexMap;     // 参数名到索引的映射
    bool m_simulationEnabled;                   // 是否启用模拟
};

#endif // BINDABLE_GRIPPER_PROCESS_H
