#include "bindable_gripper_process.h"
#include <QVBoxLayout>
#include <QDateTime>

BindableGripperProcess::BindableGripperProcess(QWidget *parent)
    : BindableWidget(parent)  // 继承BindableWidget
    , m_gripperProcess(nullptr)
    , m_currentState(Idle)
    , m_simulationTimer(nullptr)
    , m_simulationEnabled(true)
{
    setWindowTitle("可绑定的夹爪工艺界面");
    
    // 初始化界面
    initializeInterface();
    
    // 设置控件名称
    setupControlNames();
    
    // 初始化数据绑定（关键步骤！）
    initializeBinding();
    
    // 连接按钮信号
    connectButtonSignals();
    
    // 创建模拟定时器
    m_simulationTimer = new QTimer(this);
    connect(m_simulationTimer, &QTimer::timeout, this, &BindableGripperProcess::updateSimulatedData);
    m_simulationTimer->start(6000); // 每6秒更新一次模拟数据
    
    qDebug() << "BindableGripperProcess: 可绑定夹爪界面初始化完成";
}

BindableGripperProcess::~BindableGripperProcess()
{
    // 基类析构函数会自动处理绑定清理
}

void BindableGripperProcess::initializeInterface()
{
    // 创建原始的夹爪界面作为子控件
    m_gripperProcess = new GripperProcess(this);
    
    // 设置布局
    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->addWidget(m_gripperProcess);
    layout->setContentsMargins(0, 0, 0, 0);
    setLayout(layout);
}

void BindableGripperProcess::setupControlNames()
{
    // 为原始界面中的控件设置objectName（如果没有设置的话）
    // 这样它们就可以被自动绑定
    
    // 查找所有可能的输入控件并设置名称
    QList<QWidget*> allWidgets = m_gripperProcess->findChildren<QWidget*>();
    int widgetCounter = 0;
    
    for (QWidget* widget : allWidgets) {
        if (widget->objectName().isEmpty() || widget->objectName().startsWith("qt_")) {
            QString typeName = widget->metaObject()->className();
            
            // 为不同类型的控件设置有意义的名称
            if (typeName.contains("LineEdit")) {
                widget->setObjectName(QString("gripperLineEdit_%1").arg(widgetCounter++));
            }
            else if (typeName.contains("SpinBox")) {
                widget->setObjectName(QString("gripperSpinBox_%1").arg(widgetCounter++));
            }
            else if (typeName.contains("ComboBox")) {
                widget->setObjectName(QString("gripperComboBox_%1").arg(widgetCounter++));
            }
            else if (typeName.contains("CheckBox")) {
                widget->setObjectName(QString("gripperCheckBox_%1").arg(widgetCounter++));
            }
            else if (typeName.contains("Slider")) {
                widget->setObjectName(QString("gripperSlider_%1").arg(widgetCounter++));
            }
        }
    }
    
    // 查找按钮并建立操作映射
    QList<QPushButton*> buttons = m_gripperProcess->findChildren<QPushButton*>();
    for (int i = 0; i < buttons.size(); ++i) {
        QPushButton* btn = buttons[i];
        if (btn->objectName().isEmpty() || btn->objectName().startsWith("qt_")) {
            btn->setObjectName(QString("gripperButton_%1").arg(i));
        }
        
        // 根据按钮文本建立操作映射
        QString buttonText = btn->text();
        if (buttonText.contains("张开") || buttonText.contains("Open")) {
            m_buttonOperationMap[btn->objectName()] = "open";
        } else if (buttonText.contains("闭合") || buttonText.contains("Close")) {
            m_buttonOperationMap[btn->objectName()] = "close";
        } else if (buttonText.contains("夹持") || buttonText.contains("Grip")) {
            m_buttonOperationMap[btn->objectName()] = "grip";
        } else if (buttonText.contains("释放") || buttonText.contains("Release")) {
            m_buttonOperationMap[btn->objectName()] = "release";
        } else if (buttonText.contains("归零") || buttonText.contains("Reset")) {
            m_buttonOperationMap[btn->objectName()] = "reset";
        } else if (buttonText.contains("校准") || buttonText.contains("Calibrate")) {
            m_buttonOperationMap[btn->objectName()] = "calibrate";
        }
    }
}

void BindableGripperProcess::connectButtonSignals()
{
    // 连接所有按钮的点击信号
    QList<QPushButton*> buttons = m_gripperProcess->findChildren<QPushButton*>();
    for (QPushButton* btn : buttons) {
        connect(btn, &QPushButton::clicked, this, &BindableGripperProcess::onButtonClicked);
    }
}

void BindableGripperProcess::onDataChanged(int index, const QString& name, 
                                          const QVariant& newValue, const QVariant& oldValue)
{
    qDebug() << QString("BindableGripperProcess: 夹爪参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(newValue.toString());
    
    // 验证参数值
    if (!validateParameter(name, newValue)) {
        emit parameterValidationFailed(name, newValue, "参数值超出有效范围");
        return;
    }
    
    // 发射夹爪参数变化信号
    emit gripperParameterChanged(index, name, newValue, oldValue);
    
    // 更新配置
    QJsonObject config = exportConfiguration();
    emit configurationChanged(config);
}

void BindableGripperProcess::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    qDebug() << "BindableGripperProcess: 夹爪界面绑定初始化完成，绑定了" << controlCount << "个控件";
    
    // 建立参数名到索引的映射
    m_parameterIndexMap.clear();
    for (const ControlBindInfo& info : controls) {
        m_parameterIndexMap[info.name] = info.index;
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
    
    // 更新状态显示
    updateStatusDisplay();
}

void BindableGripperProcess::onButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) return;
    
    QString buttonName = button->objectName();
    QString buttonText = button->text();
    
    qDebug() << "BindableGripperProcess: 按钮点击" << buttonName << "(" << buttonText << ")";
    
    // 处理夹爪操作
    handleGripperOperation(buttonName);
}

void BindableGripperProcess::handleGripperOperation(const QString& buttonName)
{
    if (m_buttonOperationMap.contains(buttonName)) {
        QString operation = m_buttonOperationMap[buttonName];
        
        // 根据操作类型准备参数
        QVariantMap parameters;
        if (operation == "grip") {
            parameters["force"] = 50.0;
            parameters["speed"] = 10.0;
        } else if (operation == "open" || operation == "close") {
            parameters["position"] = 100.0;
            parameters["speed"] = 20.0;
        }
        
        qDebug() << "BindableGripperProcess: 执行夹爪操作" << operation;
        
        // 更新状态
        GripperState newState = Idle;
        if (operation == "open") newState = Opening;
        else if (operation == "close") newState = Closing;
        else if (operation == "grip") newState = Gripping;
        
        updateGripperState(newState);
        
        // 发射操作请求信号
        emit gripperOperationRequested(operation, QVariant(parameters));
    }
}

void BindableGripperProcess::updateGripperState(GripperState state)
{
    if (m_currentState != state) {
        GripperState oldState = m_currentState;
        m_currentState = state;
        
        qDebug() << "BindableGripperProcess: 夹爪状态变化" 
                 << stateToString(oldState) << "->" << stateToString(state);
        
        updateStatusDisplay();
        emit gripperStateChanged(state, oldState);
    }
}

void BindableGripperProcess::executeGripperOperation(const QString& operation, const QVariant& parameters)
{
    qDebug() << "BindableGripperProcess: 收到外部操作请求" << operation;
    
    // 根据操作更新相应的控件值
    if (operation == "setForce" && parameters.isValid()) {
        double force = parameters.toDouble();
        // 查找力度相关的控件并更新
        for (const auto& info : getAllControls()) {
            if (info.name.contains("force", Qt::CaseInsensitive)) {
                updateControlValue(info.index, force);
                break;
            }
        }
    }
    else if (operation == "setPosition" && parameters.isValid()) {
        double position = parameters.toDouble();
        // 查找位置相关的控件并更新
        for (const auto& info : getAllControls()) {
            if (info.name.contains("position", Qt::CaseInsensitive)) {
                updateControlValue(info.index, position);
                break;
            }
        }
    }
    
    // 模拟操作执行
    if (operation == "open") updateGripperState(Opening);
    else if (operation == "close") updateGripperState(Closing);
    else if (operation == "grip") updateGripperState(Gripping);
    else if (operation == "reset") updateGripperState(Idle);
}

void BindableGripperProcess::resetAllParameters()
{
    // 重置所有参数为默认值
    QList<ControlBindInfo> controls = getAllControls();
    for (const ControlBindInfo& info : controls) {
        QVariant defaultValue;
        if (info.type.contains("SpinBox")) {
            defaultValue = 0;
        } else if (info.type.contains("CheckBox")) {
            defaultValue = false;
        } else if (info.type.contains("ComboBox")) {
            defaultValue = 0;
        } else if (info.type.contains("LineEdit")) {
            defaultValue = "";
        } else if (info.type.contains("Slider")) {
            defaultValue = 0;
        }
        
        if (defaultValue.isValid()) {
            updateControlValue(info.index, defaultValue);
        }
    }
    
    updateGripperState(Idle);
    qDebug() << "BindableGripperProcess: 重置所有参数";
}

void BindableGripperProcess::simulateGripperData()
{
    if (m_simulationEnabled) {
        updateSimulatedData();
    }
}

void BindableGripperProcess::updateSimulatedData()
{
    if (!isBindingInitialized()) return;
    
    // 模拟一些夹爪数据的更新
    static int counter = 0;
    counter++;
    
    QList<ControlBindInfo> controls = getAllControls();
    if (controls.isEmpty()) return;
    
    // 随机更新一个控件的值
    int randomIndex = counter % controls.size();
    const ControlBindInfo& info = controls[randomIndex];
    
    QVariant newValue;
    if (info.type.contains("SpinBox")) {
        newValue = (counter * 7) % 100;
    } else if (info.type.contains("CheckBox")) {
        newValue = (counter % 2 == 0);
    } else if (info.type.contains("ComboBox")) {
        newValue = counter % 3;
    } else if (info.type.contains("LineEdit")) {
        newValue = QString("模拟数据 %1").arg(counter);
    } else if (info.type.contains("Slider")) {
        newValue = (counter * 13) % 101;
    }
    
    if (newValue.isValid()) {
        updateControlValue(randomIndex, newValue);
        qDebug() << "BindableGripperProcess: 模拟更新" << info.name << "为" << newValue;
    }
    
    // 模拟状态变化
    if (counter % 10 == 0) {
        GripperState states[] = {Idle, Opening, Closing, Gripping};
        updateGripperState(states[counter % 4]);
    }
}

bool BindableGripperProcess::validateParameter(const QString& name, const QVariant& value)
{
    // 参数验证逻辑
    if (name.contains("force", Qt::CaseInsensitive)) {
        double force = value.toDouble();
        return force >= 0.0 && force <= 100.0;
    }
    else if (name.contains("position", Qt::CaseInsensitive)) {
        double position = value.toDouble();
        return position >= 0.0 && position <= 200.0;
    }
    else if (name.contains("speed", Qt::CaseInsensitive)) {
        double speed = value.toDouble();
        return speed >= 0.0 && speed <= 50.0;
    }

    return true; // 默认认为有效
}

QString BindableGripperProcess::stateToString(GripperState state) const
{
    switch (state) {
    case Idle: return "空闲";
    case Opening: return "张开中";
    case Closing: return "闭合中";
    case Gripping: return "夹持中";
    case Error: return "错误";
    default: return "未知";
    }
}

void BindableGripperProcess::updateStatusDisplay()
{
    // 这里可以更新状态显示控件
    qDebug() << "BindableGripperProcess: 当前状态" << stateToString(m_currentState);
}

QMap<int, QVariant> BindableGripperProcess::getAllParameterData() const
{
    return getAllValues();
}

QJsonObject BindableGripperProcess::exportConfiguration() const
{
    QJsonObject config;

    // 导出控件数据
    QJsonObject controlData;
    QList<ControlBindInfo> controls = getAllControls();
    for (const ControlBindInfo& info : controls) {
        QJsonObject controlInfo;
        controlInfo["name"] = info.name;
        controlInfo["type"] = info.type;
        controlInfo["value"] = info.value.toString();
        controlData[QString::number(info.index)] = controlInfo;
    }

    // 导出状态信息
    QJsonObject stateInfo;
    stateInfo["currentState"] = static_cast<int>(m_currentState);
    stateInfo["stateString"] = stateToString(m_currentState);
    stateInfo["simulationEnabled"] = m_simulationEnabled;

    config["controls"] = controlData;
    config["state"] = stateInfo;
    config["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    config["version"] = "1.0.0";

    return config;
}

bool BindableGripperProcess::importConfiguration(const QJsonObject& config)
{
    if (!config.contains("controls")) {
        qWarning() << "BindableGripperProcess: 配置中缺少controls字段";
        return false;
    }

    QJsonObject controlData = config["controls"].toObject();
    int successCount = 0;

    for (auto it = controlData.begin(); it != controlData.end(); ++it) {
        bool ok;
        int index = it.key().toInt(&ok);
        if (!ok) continue;

        QJsonObject controlInfo = it.value().toObject();
        if (!controlInfo.contains("value")) continue;

        QString valueStr = controlInfo["value"].toString();
        QVariant value;

        // 根据控件类型转换值
        if (index < getControlCount()) {
            ControlBindInfo info = getControlInfo(index);
            if (info.type.contains("SpinBox")) {
                value = valueStr.toDouble();
            } else if (info.type.contains("CheckBox")) {
                value = (valueStr == "true" || valueStr == "1");
            } else if (info.type.contains("ComboBox")) {
                value = valueStr.toInt();
            } else {
                value = valueStr;
            }

            if (updateControlValue(index, value)) {
                successCount++;
            }
        }
    }

    // 导入状态信息
    if (config.contains("state")) {
        QJsonObject stateInfo = config["state"].toObject();
        if (stateInfo.contains("currentState")) {
            int state = stateInfo["currentState"].toInt();
            updateGripperState(static_cast<GripperState>(state));
        }
        if (stateInfo.contains("simulationEnabled")) {
            m_simulationEnabled = stateInfo["simulationEnabled"].toBool();
        }
    }

    qDebug() << "BindableGripperProcess: 导入配置完成，成功导入" << successCount << "个控件数据";
    return successCount > 0;
}
