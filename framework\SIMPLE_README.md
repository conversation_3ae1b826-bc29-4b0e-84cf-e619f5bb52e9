# 简单UI数据绑定框架

一个轻量级、清晰简单的Qt UI数据绑定框架，专注于核心功能。

## 🎯 核心功能

### ✅ 自动控件扫描
- 递归扫描界面中的所有控件
- 自动为支持的控件分配序号（从0开始）
- 生成带序号的控件表

### ✅ 简单信号机制
- **初始化完成信号**: `initialized(int controlCount, QList<SimpleControlInfo> controls)`
- **数据变化信号**: `dataChanged(int index, QString name, QVariant value, QVariant oldValue)`

### ✅ 外部数据更新
- 通过序号更新: `updateValue(int index, QVariant value)`
- 通过控件名更新: `updateValueByName(QString name, QVariant value)`

## 🚀 3步快速使用

### 第1步：创建绑定器
```cpp
SimpleUIBinder* binder = new SimpleUIBinder(this, this);
```

### 第2步：连接信号
```cpp
connect(binder, &SimpleUIBinder::initialized, this, &MyClass::onInitialized);
connect(binder, &SimpleUIBinder::dataChanged, this, &MyClass::onDataChanged);
```

### 第3步：初始化
```cpp
binder->initialize();
```

## 📋 完整示例

```cpp
class MyWidget : public QWidget {
    Q_OBJECT
    
public:
    MyWidget(QWidget* parent = nullptr) : QWidget(parent) {
        setupUi(this); // Qt Designer生成的UI
        
        // 创建绑定器
        m_binder = new SimpleUIBinder(this, this);
        
        // 连接信号
        connect(m_binder, &SimpleUIBinder::initialized,
                this, &MyWidget::onInitialized);
        connect(m_binder, &SimpleUIBinder::dataChanged,
                this, &MyWidget::onDataChanged);
        
        // 初始化
        m_binder->initialize();
    }
    
private slots:
    void onInitialized(int count, const QList<SimpleControlInfo>& controls) {
        qDebug() << "绑定了" << count << "个控件";
        for (const auto& info : controls) {
            qDebug() << "[" << info.index << "]" << info.name 
                     << "=" << info.value;
        }
    }
    
    void onDataChanged(int index, const QString& name, 
                      const QVariant& value, const QVariant& oldValue) {
        qDebug() << "控件" << index << name << "从" << oldValue 
                 << "变为" << value;
        
        // 在这里处理数据变化，比如：
        // - 发送给外部系统
        // - 保存到配置文件
        // - 更新其他界面
    }
    
private:
    SimpleUIBinder* m_binder;
};
```

## 🔧 支持的控件类型

- ✅ **QLineEdit** - 文本输入框
- ✅ **QComboBox** - 下拉选择框
- ✅ **QCheckBox** - 复选框
- ✅ **QRadioButton** - 单选按钮
- ✅ **QSpinBox** - 整数输入框
- ✅ **QDoubleSpinBox** - 浮点数输入框
- ✅ **QSlider** - 滑块
- ✅ **QTextEdit** - 多行文本编辑器
- ✅ **QPlainTextEdit** - 纯文本编辑器

## 📊 控件信息结构

```cpp
struct SimpleControlInfo {
    int index;          // 控件序号（从0开始）
    QWidget* widget;    // 控件指针
    QString name;       // 控件名称（objectName）
    QString type;       // 控件类型（类名）
    QVariant value;     // 当前值
};
```

## 🎮 外部控制接口

```cpp
// 通过序号更新控件值
binder->updateValue(0, "新文本");
binder->updateValue(1, 42);
binder->updateValue(2, true);

// 通过控件名更新值
binder->updateValueByName("nameEdit", "新名称");
binder->updateValueByName("countSpin", 100);

// 获取控件当前值
QVariant value = binder->getValue(0);

// 获取控件信息
SimpleControlInfo info = binder->getControlInfo(0);

// 获取所有控件信息
QList<SimpleControlInfo> allControls = binder->getAllControls();
```

## ⚠️ 重要提示

### 控件命名要求
- **必须设置objectName**: 只有设置了objectName的控件才会被绑定
- **名称不能为空**: 空名称的控件会被忽略
- **避免Qt默认名称**: 以"qt_"开头的默认名称会被忽略

### 最佳实践
1. 在Qt Designer中为所有需要绑定的控件设置有意义的objectName
2. 使用一致的命名约定，如：`speedSpinBox`、`enabledCheckBox`
3. 在`initialized`信号中记录控件序号表，便于后续使用
4. 在`dataChanged`信号中处理数据同步逻辑

## 📁 文件结构

```
framework/
├── simple_ui_binder.h         # 核心绑定器头文件
├── simple_ui_binder.cpp       # 核心绑定器实现
├── simple_example.h           # 使用示例头文件
├── simple_example.cpp         # 使用示例实现
├── simple_main.cpp            # 示例程序入口
├── enhanced_gripper_simple.h  # 现有界面集成示例
└── enhanced_gripper_simple.cpp
```

## 🚀 运行示例

```bash
# 编译示例程序
cd framework
qmake
make

# 运行简单示例
./simple_example

# 或者在主程序中查看集成示例
```

## 🔄 与现有界面集成

参考 `enhanced_gripper_simple.h/cpp` 了解如何将框架集成到现有的Qt Designer界面中：

1. 继承现有的界面类
2. 为控件设置objectName（如果没有的话）
3. 创建SimpleUIBinder并初始化
4. 处理初始化和数据变化信号
5. 提供外部接口用于数据更新

## 💡 设计理念

这个框架遵循"简单就是美"的设计理念：

- **单一职责**: 只专注于UI数据绑定
- **清晰接口**: 只有两个核心信号和几个简单方法
- **零配置**: 无需复杂的配置，自动扫描即可使用
- **轻量级**: 代码简洁，性能优秀
- **易集成**: 可以轻松集成到现有项目中

开始使用这个简单而强大的数据绑定框架吧！
