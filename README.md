# 基于继承的UI数据绑定框架

一个优雅的Qt UI数据绑定框架，通过继承方式实现自动数据绑定和信号槽通知。

## 🎯 核心特性

- ✅ **继承模式设计** - 数据绑定成为界面类的内在能力
- ✅ **自动控件扫描** - 递归扫描并自动分配序号
- ✅ **虚函数机制** - 重写虚函数自定义数据处理
- ✅ **信号槽通知** - 自动发射信号通知外部系统
- ✅ **零配置使用** - 无需复杂配置，开箱即用

## 🚀 快速开始

### 第1步：继承BindableWidget
```cpp
// 让您的界面类继承BindableWidget而不是QWidget
class MyWidget : public BindableWidget {
    Q_OBJECT
    
public:
    MyWidget(QWidget* parent = nullptr) : BindableWidget(parent) {
        setupUi(this);        // Qt Designer生成的UI设置
        initializeBinding();  // 初始化数据绑定
    }
};
```

### 第2步：重写虚函数处理数据变化
```cpp
protected:
    void onDataChanged(int index, const QString& name, 
                      const QVariant& newValue, const QVariant& oldValue) override {
        // 处理数据变化
        qDebug() << "控件" << name << "从" << oldValue << "变为" << newValue;
        
        // 添加您的业务逻辑：
        // - 发送给外部系统
        // - 保存到数据库
        // - 更新其他界面
        processDataChange(name, newValue);
    }
    
    void onBindingInitialized(int controlCount, 
                             const QList<ControlBindInfo>& controls) override {
        // 绑定初始化完成
        qDebug() << "绑定了" << controlCount << "个控件";
        
        // 可以在这里建立控件名到序号的映射
        for (const auto& info : controls) {
            m_controlIndexMap[info.name] = info.index;
        }
    }
```

### 第3步：外部控制（可选）
```cpp
// 外部可以通过序号或名称更新控件
myWidget->updateControlValue(0, "新值");
myWidget->updateControlValueByName("nameEdit", "新名称");

// 外部可以获取控件当前值
QVariant value = myWidget->getControlValue(0);

// 外部可以连接信号监听数据变化
connect(myWidget, &BindableWidget::controlDataChanged,
        this, &ExternalClass::onDataChanged);
```

## 📁 项目结构

```
framework/
├── bindable_widget.h/cpp           # 核心基类
├── inherited_example.h/cpp         # 基础使用示例
├── bindable_gripper_process.h/cpp  # 现有界面改造示例
└── INHERITANCE_GUIDE.md            # 详细使用指南

原始界面/
├── gripperprocess.h/cpp            # 原始夹爪界面
├── gripperprocessparam.h/cpp       # 原始夹爪参数界面
├── screwingprocess.h/cpp           # 原始拧紧界面
└── screwingprocessparam.h/cpp      # 原始拧紧参数界面

主程序/
├── simple_mainwindow.h/cpp         # 演示主窗口
├── main.cpp                        # 程序入口
└── Module_UI.pro                   # 项目文件
```

## 🎮 演示程序

运行程序可以看到6个标签页：

1. **原始夹爪界面** - 未使用框架的原始界面
2. **原始夹爪参数** - 未使用框架的原始界面
3. **原始拧紧界面** - 未使用框架的原始界面
4. **原始拧紧参数** - 未使用框架的原始界面
5. **继承模式示例** ⭐ - 展示基础继承模式功能
6. **可绑定夹爪界面** ⭐ - 展示现有界面改造示例

## 🔧 支持的控件类型

- ✅ QLineEdit（文本输入）
- ✅ QComboBox（下拉选择）
- ✅ QCheckBox（复选框）
- ✅ QRadioButton（单选按钮）
- ✅ QSpinBox（整数输入）
- ✅ QDoubleSpinBox（浮点数输入）
- ✅ QSlider（滑块）
- ✅ QTextEdit（多行文本）
- ✅ QPlainTextEdit（纯文本）

## ⚠️ 重要提示

### 控件命名要求
- **必须设置objectName**：只有设置了objectName的控件才会被绑定
- **名称不能为空**：空名称的控件会被忽略
- **使用有意义的名称**：如`speedSpinBox`、`enabledCheckBox`等

### 最佳实践
1. 在Qt Designer中为所有需要绑定的控件设置有意义的objectName
2. 在`onBindingInitialized()`中建立控件名到序号的映射
3. 在`onDataChanged()`中处理数据同步逻辑
4. 使用序号进行外部数据更新（性能更好）

## 🚀 编译运行

```bash
# 编译项目
qmake
make

# 运行程序
./Module_UI
```

## 💡 为什么选择继承模式

相比组合模式，继承模式具有以下优势：

- 🎯 **更优雅的设计** - 数据绑定成为界面类的内在能力
- 🚀 **更简洁的代码** - 无需管理额外的绑定器对象
- ⚡ **更好的性能** - 无额外对象开销
- 🔧 **更强的扩展性** - 支持虚函数重写自定义行为
- 🎨 **更自然的使用** - 符合面向对象设计原则

## 📚 详细文档

- [继承模式详细指南](framework/INHERITANCE_GUIDE.md)
- [完整总结文档](FINAL_SUMMARY.md)

## 🎉 开始使用

现在您可以开始在您的项目中使用这个优雅的继承模式数据绑定框架了！

只需要：
1. 复制`framework/bindable_widget.h/cpp`到您的项目
2. 让您的界面类继承`BindableWidget`
3. 在构造函数中调用`initializeBinding()`
4. 重写虚函数处理数据变化

就这么简单！🎉
