#include "simple_mainwindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QSplitter>
#include <QDebug>

SimpleMainWindow::SimpleMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_tabWidget(nullptr)
    , m_gripperProcess(nullptr)
    , m_gripperProcessParam(nullptr)
    , m_screwingProcess(nullptr)
    , m_screwingProcessParam(nullptr)
    , m_simpleExample(nullptr)
    , m_enhancedGripper(nullptr)
    , m_inheritedExample(nullptr)
    , m_bindableGripper(nullptr)
    , m_logDisplay(nullptr)
    , m_statusLabel(nullptr)
    , m_currentTabLabel(nullptr)
{
    setWindowTitle("简单UI数据绑定框架 - 演示程序");
    setMinimumSize(1200, 800);
    
    createMenus();
    createStatusBar();
    createUI();
    
    // 设置初始状态
    m_statusLabel->setText("就绪");
    m_currentTabLabel->setText("当前: 原始夹爪界面");
}

SimpleMainWindow::~SimpleMainWindow()
{
    // Qt的父子关系会自动处理内存清理
}

void SimpleMainWindow::createMenus()
{
    // 帮助菜单
    QMenu* helpMenu = menuBar()->addMenu("帮助(&H)");
    
    QAction* aboutAction = new QAction("关于(&A)", this);
    connect(aboutAction, &QAction::triggered, this, &SimpleMainWindow::showAbout);
    helpMenu->addAction(aboutAction);
}

void SimpleMainWindow::createStatusBar()
{
    m_statusLabel = new QLabel("就绪");
    m_currentTabLabel = new QLabel("当前: 原始夹爪界面");
    
    statusBar()->addWidget(m_statusLabel);
    statusBar()->addPermanentWidget(m_currentTabLabel);
}

void SimpleMainWindow::createUI()
{
    // 创建主分割器
    QSplitter* mainSplitter = new QSplitter(Qt::Vertical, this);
    
    // 创建标签页控件
    m_tabWidget = new QTabWidget();
    connect(m_tabWidget, &QTabWidget::currentChanged, this, &SimpleMainWindow::onTabChanged);
    
    // 添加原始界面
    m_gripperProcess = new GripperProcess();
    m_tabWidget->addTab(m_gripperProcess, "原始夹爪界面");
    
    m_gripperProcessParam = new GripperProcessParam();
    m_tabWidget->addTab(m_gripperProcessParam, "原始夹爪参数");
    
    m_screwingProcess = new ScrewingProcess();
    m_tabWidget->addTab(m_screwingProcess, "原始拧紧界面");
    
    m_screwingProcessParam = new ScrewingProcessParam();
    m_tabWidget->addTab(m_screwingProcessParam, "原始拧紧参数");
    
    // 添加简单框架示例
    m_simpleExample = new SimpleExample();
    m_tabWidget->addTab(m_simpleExample, "简单框架示例");
    
    // 添加增强版夹爪界面
    m_enhancedGripper = new EnhancedGripperSimple();
    m_tabWidget->addTab(m_enhancedGripper, "增强版夹爪界面");

    // 添加继承模式示例
    m_inheritedExample = new InheritedExample();
    m_tabWidget->addTab(m_inheritedExample, "继承模式示例");

    // 添加可绑定夹爪界面
    m_bindableGripper = new BindableGripperProcess();
    m_tabWidget->addTab(m_bindableGripper, "可绑定夹爪界面");
    
    // 连接增强版夹爪界面的信号
    connect(m_enhancedGripper, &EnhancedGripperSimple::interfaceInitialized,
            this, &SimpleMainWindow::onGripperInitialized);
    connect(m_enhancedGripper, &EnhancedGripperSimple::parameterChanged,
            this, &SimpleMainWindow::onGripperParameterChanged);
    connect(m_enhancedGripper, &EnhancedGripperSimple::gripperOperationRequested,
            this, &SimpleMainWindow::onGripperOperationRequested);
    
    // 添加到分割器
    mainSplitter->addWidget(m_tabWidget);
    mainSplitter->addWidget(createLogPanel());
    
    // 设置分割器比例
    mainSplitter->setStretchFactor(0, 3);
    mainSplitter->setStretchFactor(1, 1);
    
    setCentralWidget(mainSplitter);
}

QWidget* SimpleMainWindow::createLogPanel()
{
    QGroupBox* logGroup = new QGroupBox("系统日志");
    QVBoxLayout* layout = new QVBoxLayout(logGroup);
    
    m_logDisplay = new QTextEdit();
    m_logDisplay->setMaximumHeight(200);
    m_logDisplay->setReadOnly(true);
    m_logDisplay->append("=== 简单UI数据绑定框架演示程序启动 ===");
    m_logDisplay->append("请切换到不同的标签页查看各种功能演示");
    
    layout->addWidget(m_logDisplay);
    
    return logGroup;
}

void SimpleMainWindow::showAbout()
{
    QString aboutText = 
        "<h2>简单UI数据绑定框架</h2>"
        "<p><b>版本:</b> 1.0.0</p>"
        "<p>这是一个轻量级、清晰简单的Qt UI数据绑定框架演示程序。</p>"
        "<h3>核心功能:</h3>"
        "<ul>"
        "<li>自动控件扫描和序号分配</li>"
        "<li>简单的信号机制</li>"
        "<li>外部数据更新接口</li>"
        "<li>零配置，易集成</li>"
        "</ul>"
        "<h3>标签页说明:</h3>"
        "<ul>"
        "<li><b>原始界面:</b> 未使用框架的原始Qt Designer界面</li>"
        "<li><b>简单框架示例:</b> 展示框架基本功能的示例</li>"
        "<li><b>增强版夹爪界面:</b> 在原始界面基础上集成框架的示例</li>"
        "</ul>"
        "<p>查看系统日志了解框架的工作过程。</p>";
    
    QMessageBox::about(this, "关于", aboutText);
}

void SimpleMainWindow::onTabChanged(int index)
{
    QStringList tabNames = {
        "原始夹爪界面", "原始夹爪参数", "原始拧紧界面",
        "原始拧紧参数", "简单框架示例", "增强版夹爪界面",
        "继承模式示例", "可绑定夹爪界面"
    };
    
    if (index >= 0 && index < tabNames.size()) {
        m_currentTabLabel->setText("当前: " + tabNames[index]);
        m_statusLabel->setText("切换到: " + tabNames[index]);

        m_logDisplay->append(QString("=== 切换到标签页: %1 ===").arg(tabNames[index]));

        if (index == 4) { // 简单框架示例
            m_logDisplay->append("这个标签页展示了简单数据绑定框架的基本功能");
        } else if (index == 5) { // 增强版夹爪界面
            m_logDisplay->append("这个标签页展示了如何在现有界面中集成数据绑定框架");
        } else if (index == 6) { // 继承模式示例
            m_logDisplay->append("这个标签页展示了基于继承的数据绑定框架");
        } else if (index == 7) { // 可绑定夹爪界面
            m_logDisplay->append("这个标签页展示了如何将现有界面改造为继承模式");
        } else {
            m_logDisplay->append("这是原始的Qt Designer界面，未使用数据绑定框架");
        }
    }
}

void SimpleMainWindow::onGripperInitialized(int controlCount, const QList<SimpleControlInfo>& controls)
{
    //m_logDisplay->append(QString("增强版夹爪界面初始化完成，绑定了 %1 个控件:").arg(controlCount));
    
    for (const SimpleControlInfo& info : controls) {
        m_logDisplay->append(QString("  [%1] %2 (%3) = %4")
                            .arg(info.index)
                            .arg(info.name)
                            .arg(info.type)
                            .arg(info.value.toString()));
    }
    
    m_statusLabel->setText(QString("增强版夹爪界面已绑定 %1 个控件").arg(controlCount));
}

void SimpleMainWindow::onGripperParameterChanged(int index, const QString& name, 
                                                const QVariant& value, const QVariant& oldValue)
{
    m_logDisplay->append(QString("夹爪参数变化: [%1] %2: %3 -> %4")
                        .arg(index)
                        .arg(name)
                        .arg(oldValue.toString())
                        .arg(value.toString()));
    
    // 滚动到底部
    m_logDisplay->moveCursor(QTextCursor::End);
}

void SimpleMainWindow::onGripperOperationRequested(const QString& operation, const QVariant& parameters)
{
    m_logDisplay->append(QString("夹爪操作请求: %1").arg(operation));
    
    if (parameters.isValid()) {
        if (parameters.type() == QVariant::Map) {
            QVariantMap paramMap = parameters.toMap();
            for (auto it = paramMap.begin(); it != paramMap.end(); ++it) {
                m_logDisplay->append(QString("  参数: %1 = %2").arg(it.key()).arg(it.value().toString()));
            }
        } else {
            m_logDisplay->append(QString("  参数: %1").arg(parameters.toString()));
        }
    }
    
    // 这里可以添加实际的夹爪控制逻辑
    m_statusLabel->setText(QString("执行夹爪操作: %1").arg(operation));
}
