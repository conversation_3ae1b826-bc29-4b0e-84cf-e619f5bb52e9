#ifndef ENHANCED_GRIPPER_SIMPLE_H
#define ENHANCED_GRIPPER_SIMPLE_H

#include <QWidget>
#include <QTimer>
#include <QDebug>
#include "simple_ui_binder.h"
#include "../gripperprocess.h"

/**
 * @brief 使用简单数据绑定框架的增强版夹爪界面
 * 
 * 演示如何在现有的Qt Designer界面中集成简单数据绑定框架
 */
class EnhancedGripperSimple : public GripperProcess
{
    Q_OBJECT

public:
    explicit EnhancedGripperSimple(QWidget *parent = nullptr);
    ~EnhancedGripperSimple();

    /**
     * @brief 获取所有控件数据
     * @return 控件序号到值的映射
     */
    QMap<int, QVariant> getAllData() const;

    /**
     * @brief 通过序号更新控件数据
     * @param index 控件序号
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateData(int index, const QVariant& value);

    /**
     * @brief 通过控件名更新数据
     * @param name 控件名称
     * @param value 新值
     * @return 是否更新成功
     */
    bool updateDataByName(const QString& name, const QVariant& value);

    /**
     * @brief 获取控件信息列表
     * @return 控件信息列表
     */
    QList<SimpleControlInfo> getControlList() const;

public slots:
    /**
     * @brief 模拟夹爪状态更新
     */
    void simulateGripperUpdate();

    /**
     * @brief 重置所有参数
     */
    void resetParameters();

signals:
    /**
     * @brief 界面初始化完成
     * @param controlCount 控件数量
     * @param controls 控件列表
     */
    void interfaceInitialized(int controlCount, const QList<SimpleControlInfo>& controls);

    /**
     * @brief 参数数据发生变化
     * @param index 控件序号
     * @param name 控件名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void parameterChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue);

    /**
     * @brief 夹爪操作请求
     * @param operation 操作类型
     * @param parameters 操作参数
     */
    void gripperOperationRequested(const QString& operation, const QVariant& parameters);

private slots:
    /**
     * @brief 处理绑定初始化完成
     * @param controlCount 控件数量
     * @param controls 控件信息列表
     */
    void onBindingInitialized(int controlCount, const QList<SimpleControlInfo>& controls);

    /**
     * @brief 处理数据变化
     * @param index 控件序号
     * @param name 控件名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void onDataChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue);

    /**
     * @brief 处理按钮点击
     */
    void onButtonClicked();

    /**
     * @brief 定时更新模拟数据
     */
    void updateSimulatedData();

private:
    /**
     * @brief 初始化数据绑定
     */
    void initializeDataBinding();

    /**
     * @brief 连接按钮信号
     */
    void connectButtonSignals();

    /**
     * @brief 为界面控件设置objectName（如果没有的话）
     */
    void setupControlNames();

    /**
     * @brief 处理特定的夹爪操作
     * @param buttonName 按钮名称
     */
    void handleGripperOperation(const QString& buttonName);

private:
    SimpleUIBinder* m_binder;           // 简单数据绑定器
    QTimer* m_simulationTimer;          // 模拟数据定时器
    bool m_bindingInitialized;          // 绑定是否已初始化
    QMap<QString, QString> m_buttonOperationMap; // 按钮名到操作的映射
};

#endif // ENHANCED_GRIPPER_SIMPLE_H
