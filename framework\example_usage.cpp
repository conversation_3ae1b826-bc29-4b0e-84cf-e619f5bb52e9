#include "example_usage.h"
#include <QApplication>
#include <QFileDialog>
#include <QJsonDocument>
#include <QDebug>

ExampleUsage::ExampleUsage(QWidget *parent)
    : QWidget(parent)
    , m_nameEdit(nullptr)
    , m_type<PERSON><PERSON>o(nullptr)
    , m_enabledCheck(nullptr)
    , m_countSpin(nullptr)
    , m_valueSpin(nullptr)
    , m_progressSlider(nullptr)
    , m_descriptionEdit(nullptr)
    , m_logDisplay(nullptr)
    , m_dataBinder(nullptr)
    , m_dataModel(nullptr)
{
    setWindowTitle("UI数据绑定框架示例");
    setMinimumSize(800, 600);
    
    createUI();
    initializeDataBinding();
    setupDataValidator();
    
    // 创建示例数据
    m_sampleData["0"] = "示例名称";
    m_sampleData["1"] = 2;
    m_sampleData["2"] = true;
    m_sampleData["3"] = 42;
    m_sampleData["4"] = 3.14159;
    m_sampleData["5"] = 75;
    m_sampleData["6"] = "这是一个示例描述文本，用于演示文本编辑控件的数据绑定功能。";
}

ExampleUsage::~ExampleUsage()
{
    delete m_dataBinder;
    delete m_dataModel;
}

void ExampleUsage::createUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // 创建控件组
    mainLayout->addWidget(createBasicControlsGroup());
    mainLayout->addWidget(createNumericControlsGroup());
    mainLayout->addWidget(createTextControlsGroup());
    mainLayout->addWidget(createActionButtonsGroup());
    mainLayout->addWidget(createInfoDisplayGroup());
    
    setLayout(mainLayout);
}

QGroupBox* ExampleUsage::createBasicControlsGroup()
{
    QGroupBox* group = new QGroupBox("基本控件");
    QGridLayout* layout = new QGridLayout(group);
    
    // 名称输入
    layout->addWidget(new QLabel("名称:"), 0, 0);
    m_nameEdit = new QLineEdit();
    m_nameEdit->setObjectName("nameEdit");
    m_nameEdit->setText("默认名称");
    layout->addWidget(m_nameEdit, 0, 1);
    
    // 类型选择
    layout->addWidget(new QLabel("类型:"), 1, 0);
    m_typeCombo = new QComboBox();
    m_typeCombo->setObjectName("typeCombo");
    m_typeCombo->addItems({"类型A", "类型B", "类型C", "类型D"});
    m_typeCombo->setCurrentIndex(0);
    layout->addWidget(m_typeCombo, 1, 1);
    
    // 启用状态
    m_enabledCheck = new QCheckBox("启用");
    m_enabledCheck->setObjectName("enabledCheck");
    m_enabledCheck->setChecked(false);
    layout->addWidget(m_enabledCheck, 2, 0, 1, 2);
    
    return group;
}

QGroupBox* ExampleUsage::createNumericControlsGroup()
{
    QGroupBox* group = new QGroupBox("数值控件");
    QGridLayout* layout = new QGridLayout(group);
    
    // 计数器
    layout->addWidget(new QLabel("计数:"), 0, 0);
    m_countSpin = new QSpinBox();
    m_countSpin->setObjectName("countSpin");
    m_countSpin->setRange(0, 100);
    m_countSpin->setValue(10);
    layout->addWidget(m_countSpin, 0, 1);
    
    // 数值
    layout->addWidget(new QLabel("数值:"), 1, 0);
    m_valueSpin = new QDoubleSpinBox();
    m_valueSpin->setObjectName("valueSpin");
    m_valueSpin->setRange(0.0, 10.0);
    m_valueSpin->setDecimals(3);
    m_valueSpin->setValue(1.000);
    layout->addWidget(m_valueSpin, 1, 1);
    
    // 进度滑块
    layout->addWidget(new QLabel("进度:"), 2, 0);
    m_progressSlider = new QSlider(Qt::Horizontal);
    m_progressSlider->setObjectName("progressSlider");
    m_progressSlider->setRange(0, 100);
    m_progressSlider->setValue(50);
    layout->addWidget(m_progressSlider, 2, 1);
    
    return group;
}

QGroupBox* ExampleUsage::createTextControlsGroup()
{
    QGroupBox* group = new QGroupBox("文本控件");
    QVBoxLayout* layout = new QVBoxLayout(group);
    
    layout->addWidget(new QLabel("描述:"));
    m_descriptionEdit = new QTextEdit();
    m_descriptionEdit->setObjectName("descriptionEdit");
    m_descriptionEdit->setMaximumHeight(100);
    m_descriptionEdit->setPlainText("默认描述文本");
    layout->addWidget(m_descriptionEdit);
    
    return group;
}

QGroupBox* ExampleUsage::createActionButtonsGroup()
{
    QGroupBox* group = new QGroupBox("操作按钮");
    QHBoxLayout* layout = new QHBoxLayout(group);
    
    m_resetButton = new QPushButton("重置默认值");
    m_exportButton = new QPushButton("导出数据");
    m_importButton = new QPushButton("导入数据");
    m_infoButton = new QPushButton("绑定信息");
    m_updateButton = new QPushButton("模拟更新");
    m_saveButton = new QPushButton("保存文件");
    m_loadButton = new QPushButton("加载文件");
    
    layout->addWidget(m_resetButton);
    layout->addWidget(m_exportButton);
    layout->addWidget(m_importButton);
    layout->addWidget(m_infoButton);
    layout->addWidget(m_updateButton);
    layout->addWidget(m_saveButton);
    layout->addWidget(m_loadButton);
    
    // 连接信号
    connect(m_resetButton, &QPushButton::clicked, this, &ExampleUsage::onResetToDefaults);
    connect(m_exportButton, &QPushButton::clicked, this, &ExampleUsage::onExportData);
    connect(m_importButton, &QPushButton::clicked, this, &ExampleUsage::onImportData);
    connect(m_infoButton, &QPushButton::clicked, this, &ExampleUsage::onShowBindingInfo);
    connect(m_updateButton, &QPushButton::clicked, this, &ExampleUsage::onSimulateExternalUpdate);
    connect(m_saveButton, &QPushButton::clicked, this, &ExampleUsage::onSaveData);
    connect(m_loadButton, &QPushButton::clicked, this, &ExampleUsage::onLoadData);
    
    return group;
}

QGroupBox* ExampleUsage::createInfoDisplayGroup()
{
    QGroupBox* group = new QGroupBox("信息显示");
    QVBoxLayout* layout = new QVBoxLayout(group);
    
    m_logDisplay = new QTextEdit();
    m_logDisplay->setMaximumHeight(150);
    m_logDisplay->setReadOnly(true);
    m_logDisplay->append("数据绑定框架初始化完成");
    layout->addWidget(m_logDisplay);
    
    return group;
}

void ExampleUsage::initializeDataBinding()
{
    // 创建数据绑定器
    m_dataBinder = new UIDataBinder(this, this);
    
    // 创建数据模型
    m_dataModel = new UIDataModel(this);
    
    // 连接信号
    connect(m_dataBinder, &UIDataBinder::dataChanged, 
            this, &ExampleUsage::onDataChanged);
    connect(m_dataBinder, &UIDataBinder::controlValueChanged,
            this, &ExampleUsage::onControlValueChanged);
    connect(m_dataBinder, &UIDataBinder::validationFailed,
            this, &ExampleUsage::onValidationFailed);
    
    // 初始化绑定
    int controlCount = m_dataBinder->initializeBinding();
    m_logDisplay->append(QString("成功绑定 %1 个控件").arg(controlCount));
    
    // 将绑定的数据同步到数据模型
    QMap<int, QVariant> allValues = m_dataBinder->getAllValues();
    for (auto it = allValues.begin(); it != allValues.end(); ++it) {
        ControlInfo info = m_dataBinder->getControlInfo(it.key());
        m_dataModel->setDataItem(it.key(), it.value(), info.description, info.group);
    }
    
    // 启用自动保存
    m_dataModel->enableAutoSave("example_autosave.json", 10000); // 10秒自动保存
}

void ExampleUsage::setupDataValidator()
{
    // 设置数据验证器
    auto validator = [this](int index, const QVariant& value) -> bool {
        ControlInfo info = m_dataBinder->getControlInfo(index);
        
        // 对计数器进行特殊验证
        if (info.objectName == "countSpin") {
            int intValue = value.toInt();
            if (intValue < 0 || intValue > 100) {
                m_logDisplay->append(QString("验证失败: 计数值 %1 超出范围 [0, 100]").arg(intValue));
                return false;
            }
        }
        
        // 对数值进行特殊验证
        if (info.objectName == "valueSpin") {
            double doubleValue = value.toDouble();
            if (doubleValue < 0.0 || doubleValue > 10.0) {
                m_logDisplay->append(QString("验证失败: 数值 %1 超出范围 [0.0, 10.0]").arg(doubleValue));
                return false;
            }
        }
        
        return true;
    };
    
    m_dataBinder->setDataValidator(validator);
    m_dataModel->setValidator(validator);
}

void ExampleUsage::onDataChanged(int index, const QVariant& value, const QVariant& oldValue)
{
    // 同步更新数据模型
    m_dataModel->setValue(index, value);
    
    ControlInfo info = m_dataBinder->getControlInfo(index);
    m_logDisplay->append(QString("数据变化 [%1] %2: %3 -> %4")
                        .arg(index)
                        .arg(info.objectName)
                        .arg(oldValue.toString())
                        .arg(value.toString()));
}

void ExampleUsage::onControlValueChanged(const ControlInfo& controlInfo, 
                                        const QVariant& newValue, const QVariant& oldValue)
{
    Q_UNUSED(controlInfo)
    Q_UNUSED(newValue)
    Q_UNUSED(oldValue)
    // 这里可以添加更详细的控件值变化处理逻辑
}

void ExampleUsage::onValidationFailed(int index, const QVariant& value, const QString& errorMessage)
{
    ControlInfo info = m_dataBinder->getControlInfo(index);
    m_logDisplay->append(QString("验证失败 [%1] %2: %3 - %4")
                        .arg(index)
                        .arg(info.objectName)
                        .arg(value.toString())
                        .arg(errorMessage));
    
    QMessageBox::warning(this, "数据验证失败", 
                        QString("控件 %1 的值 %2 验证失败:\n%3")
                        .arg(info.objectName)
                        .arg(value.toString())
                        .arg(errorMessage));
}

void ExampleUsage::onResetToDefaults()
{
    m_dataBinder->resetToDefaults();
    m_dataModel->resetToDefault();
    m_logDisplay->append("已重置所有控件为默认值");
}

void ExampleUsage::onExportData()
{
    QJsonObject jsonData = m_dataBinder->exportToJson(true);
    QJsonDocument doc(jsonData);

    QString fileName = QFileDialog::getSaveFileName(this, "导出数据",
                                                   "data_export.json",
                                                   "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly)) {
            file.write(doc.toJson());
            file.close();
            m_logDisplay->append(QString("数据已导出到: %1").arg(fileName));
        } else {
            m_logDisplay->append("导出失败: 无法写入文件");
        }
    }
}

void ExampleUsage::onImportData()
{
    // 使用示例数据进行导入演示
    QJsonObject importData;
    importData["data"] = m_sampleData;

    int count = m_dataBinder->importFromJson(importData);
    m_dataModel->fromJson(importData);

    m_logDisplay->append(QString("已导入 %1 个数据项").arg(count));

    // 也可以从文件导入
    /*
    QString fileName = QFileDialog::getOpenFileName(this, "导入数据",
                                                   "",
                                                   "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::ReadOnly)) {
            QByteArray data = file.readAll();
            file.close();

            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(data, &error);
            if (error.error == QJsonParseError::NoError) {
                int count = m_dataBinder->importFromJson(doc.object());
                m_dataModel->fromJson(doc.object());
                m_logDisplay->append(QString("从文件导入 %1 个数据项").arg(count));
            } else {
                m_logDisplay->append("导入失败: JSON解析错误");
            }
        } else {
            m_logDisplay->append("导入失败: 无法读取文件");
        }
    }
    */
}

void ExampleUsage::onShowBindingInfo()
{
    QList<ControlInfo> allInfo = m_dataBinder->getAllControlInfo();

    QString infoText = "=== 控件绑定信息 ===\n";
    for (const ControlInfo& info : allInfo) {
        infoText += QString("索引: %1\n").arg(info.index);
        infoText += QString("对象名: %1\n").arg(info.objectName);
        infoText += QString("类型: %1\n").arg(info.controlType);
        infoText += QString("当前值: %1\n").arg(info.currentValue.toString());
        infoText += QString("默认值: %1\n").arg(info.defaultValue.toString());
        infoText += QString("只读: %1\n").arg(info.isReadOnly ? "是" : "否");
        infoText += "---\n";
    }

    QMessageBox::information(this, "绑定信息", infoText);
    m_logDisplay->append(QString("显示了 %1 个控件的绑定信息").arg(allInfo.size()));
}

void ExampleUsage::onSimulateExternalUpdate()
{
    // 模拟外部系统更新数据
    static int updateCounter = 0;
    updateCounter++;

    // 更新名称
    m_dataBinder->updateControlValueByName("nameEdit",
                                          QString("外部更新 #%1").arg(updateCounter));

    // 更新类型（循环选择）
    int currentType = m_dataBinder->getControlValueByName("typeCombo").toInt();
    int newType = (currentType + 1) % 4;
    m_dataBinder->updateControlValueByName("typeCombo", newType);

    // 切换启用状态
    bool currentEnabled = m_dataBinder->getControlValueByName("enabledCheck").toBool();
    m_dataBinder->updateControlValueByName("enabledCheck", !currentEnabled);

    // 随机更新数值
    int randomCount = qrand() % 101;
    m_dataBinder->updateControlValueByName("countSpin", randomCount);

    double randomValue = (qrand() % 10000) / 1000.0;
    m_dataBinder->updateControlValueByName("valueSpin", randomValue);

    int randomProgress = qrand() % 101;
    m_dataBinder->updateControlValueByName("progressSlider", randomProgress);

    m_logDisplay->append(QString("执行外部更新 #%1").arg(updateCounter));
}

void ExampleUsage::onSaveData()
{
    QString fileName = QFileDialog::getSaveFileName(this, "保存数据模型",
                                                   "datamodel.json",
                                                   "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        if (m_dataModel->saveToFile(fileName)) {
            m_logDisplay->append(QString("数据模型已保存到: %1").arg(fileName));
        } else {
            m_logDisplay->append("保存失败");
        }
    }
}

void ExampleUsage::onLoadData()
{
    QString fileName = QFileDialog::getOpenFileName(this, "加载数据模型",
                                                   "",
                                                   "JSON Files (*.json)");
    if (!fileName.isEmpty()) {
        if (m_dataModel->loadFromFile(fileName)) {
            // 将数据模型的数据同步到UI控件
            QMap<int, QVariant> modelData = m_dataModel->getAllValues();
            for (auto it = modelData.begin(); it != modelData.end(); ++it) {
                m_dataBinder->updateControlValue(it.key(), it.value());
            }
            m_logDisplay->append(QString("数据模型已从文件加载: %1").arg(fileName));
        } else {
            m_logDisplay->append("加载失败");
        }
    }
}
