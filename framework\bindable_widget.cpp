#include "bindable_widget.h"
#include <QDebug>

BindableWidget::BindableWidget(QWidget *parent)
    : QWidget(parent)
    , m_bindingInitialized(false)
{
}

BindableWidget::~BindableWidget()
{
    // 断开所有信号连接
    for (auto& info : m_controls) {
        disconnectControlSignal(info);
    }
}

int BindableWidget::initializeBinding(bool autoConnect)
{
    // 清除现有绑定
    for (auto& info : m_controls) {
        disconnectControlSignal(info);
    }
    m_controls.clear();
    m_nameToIndex.clear();
    m_widgetToIndex.clear();

    // 扫描所有控件
    scanControls(this);

    // 如果需要自动连接信号
    if (autoConnect) {
        for (auto& info : m_controls) {
            connectControlSignal(info);
        }
    }

    m_bindingInitialized = true;
    
    qDebug() << "BindableWidget: 初始化绑定完成，绑定了" << m_controls.size() << "个控件";
    
    // 调用虚函数通知子类
    onBindingInitialized(m_controls.size(), m_controls);
    
    // 发送信号通知外部
    emit bindingInitialized(m_controls.size(), m_controls);
    
    return m_controls.size();
}

void BindableWidget::scanControls(QWidget* widget)
{
    if (!widget) return;

    // 检查当前控件是否支持绑定
    if (isSupportedControl(widget)) {
        ControlBindInfo info;
        info.index = m_controls.size(); // 使用列表大小作为序号
        info.widget = widget;
        info.name = widget->objectName();
        info.type = getControlTypeName(widget);
        info.value = getWidgetCurrentValue(widget);
        
        // 添加到列表和映射表
        m_controls.append(info);
        if (!info.name.isEmpty()) {
            m_nameToIndex[info.name] = info.index;
        }
        m_widgetToIndex[widget] = info.index;
        
        qDebug() << "BindableWidget: 发现控件 [" << info.index << "]" 
                 << info.name << "(" << info.type << ") = " << info.value;
    }

    // 递归扫描子控件
    const QList<QWidget*> children = widget->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    for (QWidget* child : children) {
        scanControls(child);
    }
}

bool BindableWidget::isSupportedControl(QWidget* widget) const
{
    if (!widget || widget->objectName().isEmpty() || widget == this) {
        return false;
    }

    // 检查是否为支持的控件类型
    return qobject_cast<QLineEdit*>(widget) ||
           qobject_cast<QComboBox*>(widget) ||
           qobject_cast<QCheckBox*>(widget) ||
           qobject_cast<QRadioButton*>(widget) ||
           qobject_cast<QSpinBox*>(widget) ||
           qobject_cast<QDoubleSpinBox*>(widget) ||
           qobject_cast<QSlider*>(widget) ||
           qobject_cast<QTextEdit*>(widget) ||
           qobject_cast<QPlainTextEdit*>(widget);
}

QString BindableWidget::getControlTypeName(QWidget* widget) const
{
    if (!widget) return "Unknown";
    return widget->metaObject()->className();
}

bool BindableWidget::connectControlSignal(ControlBindInfo& info)
{
    if (!info.widget) return false;

    QWidget* widget = info.widget;
    
    // 根据控件类型连接相应的信号
    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        info.connection = connect(lineEdit, &QLineEdit::textChanged,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        info.connection = connect(comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        info.connection = connect(checkBox, &QCheckBox::toggled,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        info.connection = connect(radioButton, &QRadioButton::toggled,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        info.connection = connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        info.connection = connect(doubleSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        info.connection = connect(slider, &QSlider::valueChanged,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        info.connection = connect(textEdit, &QTextEdit::textChanged,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        info.connection = connect(plainTextEdit, &QPlainTextEdit::textChanged,
                                 this, &BindableWidget::handleControlValueChanged);
    }
    else {
        qWarning() << "BindableWidget: 不支持的控件类型:" << widget->metaObject()->className();
        return false;
    }

    return true;
}

void BindableWidget::disconnectControlSignal(ControlBindInfo& info)
{
    if (info.connection) {
        disconnect(info.connection);
        info.connection = QMetaObject::Connection();
    }
}

QVariant BindableWidget::getWidgetCurrentValue(QWidget* widget) const
{
    if (!widget) return QVariant();

    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        return lineEdit->text();
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        return comboBox->currentIndex();
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        return checkBox->isChecked();
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        return radioButton->isChecked();
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        return spinBox->value();
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        return doubleSpinBox->value();
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        return slider->value();
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        return textEdit->toPlainText();
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        return plainTextEdit->toPlainText();
    }

    return QVariant();
}

bool BindableWidget::setWidgetValue(QWidget* widget, const QVariant& value)
{
    if (!widget) return false;

    if (auto lineEdit = qobject_cast<QLineEdit*>(widget)) {
        lineEdit->setText(value.toString());
        return true;
    }
    else if (auto comboBox = qobject_cast<QComboBox*>(widget)) {
        comboBox->setCurrentIndex(value.toInt());
        return true;
    }
    else if (auto checkBox = qobject_cast<QCheckBox*>(widget)) {
        checkBox->setChecked(value.toBool());
        return true;
    }
    else if (auto radioButton = qobject_cast<QRadioButton*>(widget)) {
        radioButton->setChecked(value.toBool());
        return true;
    }
    else if (auto spinBox = qobject_cast<QSpinBox*>(widget)) {
        spinBox->setValue(value.toInt());
        return true;
    }
    else if (auto doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
        doubleSpinBox->setValue(value.toDouble());
        return true;
    }
    else if (auto slider = qobject_cast<QSlider*>(widget)) {
        slider->setValue(value.toInt());
        return true;
    }
    else if (auto textEdit = qobject_cast<QTextEdit*>(widget)) {
        textEdit->setPlainText(value.toString());
        return true;
    }
    else if (auto plainTextEdit = qobject_cast<QPlainTextEdit*>(widget)) {
        plainTextEdit->setPlainText(value.toString());
        return true;
    }

    return false;
}

void BindableWidget::handleControlValueChanged()
{
    QWidget* senderWidget = qobject_cast<QWidget*>(sender());
    if (!senderWidget) return;

    // 查找控件序号
    auto it = m_widgetToIndex.find(senderWidget);
    if (it == m_widgetToIndex.end()) return;

    int index = it.value();
    if (index < 0 || index >= m_controls.size()) return;

    ControlBindInfo& info = m_controls[index];
    QVariant oldValue = info.value;
    QVariant newValue = getWidgetCurrentValue(senderWidget);

    // 更新存储的值
    info.value = newValue;

    // 调用虚函数通知子类
    onDataChanged(index, info.name, newValue, oldValue);

    // 发射信号通知外部
    emit controlDataChanged(index, info.name, newValue, oldValue);
}

void BindableWidget::onDataChanged(int index, const QString& name, 
                                  const QVariant& newValue, const QVariant& oldValue)
{
    // 默认实现：输出调试信息
    qDebug() << "BindableWidget: 控件数据变化 [" << index << "]" << name 
             << ":" << oldValue << "->" << newValue;
}

void BindableWidget::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    // 默认实现：输出调试信息
    Q_UNUSED(controls)
    qDebug() << "BindableWidget: 绑定初始化完成，共" << controlCount << "个控件";
}

bool BindableWidget::updateControlValue(int index, const QVariant& value)
{
    if (index < 0 || index >= m_controls.size()) {
        qWarning() << "BindableWidget: 无效的控件序号" << index;
        return false;
    }

    ControlBindInfo& info = m_controls[index];
    if (!info.widget) {
        qWarning() << "BindableWidget: 控件指针为空，序号" << index;
        return false;
    }

    QVariant oldValue = info.value;

    // 临时断开信号连接，避免循环触发
    disconnectControlSignal(info);

    // 设置控件值
    bool success = setWidgetValue(info.widget, value);

    if (success) {
        info.value = value;
        // 调用虚函数通知子类
        onDataChanged(index, info.name, value, oldValue);
        // 发射信号通知外部
        emit controlDataChanged(index, info.name, value, oldValue);
    }

    // 重新连接信号
    connectControlSignal(info);

    return success;
}

bool BindableWidget::updateControlValueByName(const QString& name, const QVariant& value)
{
    int index = findControlIndex(name);
    if (index == -1) {
        qWarning() << "BindableWidget: 未找到名为" << name << "的控件";
        return false;
    }
    return updateControlValue(index, value);
}

int BindableWidget::updateMultipleValues(const QMap<int, QVariant>& values)
{
    int successCount = 0;
    for (auto it = values.begin(); it != values.end(); ++it) {
        if (updateControlValue(it.key(), it.value())) {
            successCount++;
        }
    }
    return successCount;
}

QVariant BindableWidget::getControlValue(int index) const
{
    if (index < 0 || index >= m_controls.size()) {
        return QVariant();
    }
    return m_controls[index].value;
}

ControlBindInfo BindableWidget::getControlInfo(int index) const
{
    if (index < 0 || index >= m_controls.size()) {
        return ControlBindInfo();
    }
    return m_controls[index];
}

QList<ControlBindInfo> BindableWidget::getAllControls() const
{
    return m_controls;
}

QMap<int, QVariant> BindableWidget::getAllValues() const
{
    QMap<int, QVariant> values;
    for (int i = 0; i < m_controls.size(); ++i) {
        values[i] = m_controls[i].value;
    }
    return values;
}

int BindableWidget::findControlIndex(const QString& name) const
{
    auto it = m_nameToIndex.find(name);
    if (it != m_nameToIndex.end()) {
        return it.value();
    }
    return -1;
}
