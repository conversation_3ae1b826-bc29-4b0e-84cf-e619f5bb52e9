#include "enhanced_gripper_simple.h"
#include "ui_gripperprocess.h"
#include <QPushButton>

EnhancedGripperSimple::EnhancedGripperSimple(QWidget *parent)
    : GripperProcess(parent)
    , m_binder(nullptr)
    , m_simulationTimer(nullptr)
    , m_bindingInitialized(false)
{
    // 设置控件名称
    setupControlNames();
    
    // 初始化数据绑定
    initializeDataBinding();
    
    // 连接按钮信号
    connectButtonSignals();
    
    // 创建模拟定时器
    m_simulationTimer = new QTimer(this);
    connect(m_simulationTimer, &QTimer::timeout, this, &EnhancedGripperSimple::updateSimulatedData);
    m_simulationTimer->start(5000); // 每5秒更新一次模拟数据
    
    qDebug() << "EnhancedGripperSimple: 增强版夹爪界面初始化完成";
}

EnhancedGripperSimple::~EnhancedGripperSimple()
{
    delete m_binder;
}

void EnhancedGripperSimple::setupControlNames()
{
    // 为界面中的关键控件设置objectName（如果Qt Designer中没有设置的话）
    // 注意：这里需要根据实际的UI结构来设置
    
    // 由于原始的gripperprocess.ui文件可能有编码问题，
    // 我们可以通过findChildren来查找控件并设置名称
    
    // 查找所有按钮并设置名称
    QList<QPushButton*> buttons = findChildren<QPushButton*>();
    for (int i = 0; i < buttons.size(); ++i) {
        QPushButton* btn = buttons[i];
        if (btn->objectName().isEmpty() || btn->objectName().startsWith("qt_")) {
            btn->setObjectName(QString("button_%1").arg(i));
        }
        
        // 建立按钮名到操作的映射
        QString buttonText = btn->text();
        if (buttonText.contains("张开")) {
            m_buttonOperationMap[btn->objectName()] = "open";
        } else if (buttonText.contains("闭合")) {
            m_buttonOperationMap[btn->objectName()] = "close";
        } else if (buttonText.contains("夹持")) {
            m_buttonOperationMap[btn->objectName()] = "grip";
        } else if (buttonText.contains("释放")) {
            m_buttonOperationMap[btn->objectName()] = "release";
        } else if (buttonText.contains("归零")) {
            m_buttonOperationMap[btn->objectName()] = "reset";
        } else if (buttonText.contains("校准")) {
            m_buttonOperationMap[btn->objectName()] = "calibrate";
        } else if (buttonText.contains("检测")) {
            m_buttonOperationMap[btn->objectName()] = "detect";
        }
    }
    
    // 查找其他可能的输入控件
    QList<QWidget*> allWidgets = findChildren<QWidget*>();
    int widgetCounter = 0;
    for (QWidget* widget : allWidgets) {
        if (widget->objectName().isEmpty() || widget->objectName().startsWith("qt_")) {
            // 根据控件类型设置名称
            QString typeName = widget->metaObject()->className();
            if (typeName.contains("Edit") || typeName.contains("Spin") || 
                typeName.contains("Combo") || typeName.contains("Check") ||
                typeName.contains("Slider")) {
                widget->setObjectName(QString("%1_%2").arg(typeName.toLower()).arg(widgetCounter++));
            }
        }
    }
}

void EnhancedGripperSimple::initializeDataBinding()
{
    // 创建简单数据绑定器
    m_binder = new SimpleUIBinder(this, this);
    
    // 连接信号
    connect(m_binder, &SimpleUIBinder::initialized,
            this, &EnhancedGripperSimple::onBindingInitialized);
    connect(m_binder, &SimpleUIBinder::dataChanged,
            this, &EnhancedGripperSimple::onDataChanged);
    
    // 初始化绑定
    m_binder->initialize();
}

void EnhancedGripperSimple::connectButtonSignals()
{
    // 连接所有按钮的点击信号
    QList<QPushButton*> buttons = findChildren<QPushButton*>();
    for (QPushButton* btn : buttons) {
        connect(btn, &QPushButton::clicked, this, &EnhancedGripperSimple::onButtonClicked);
    }
}

void EnhancedGripperSimple::onBindingInitialized(int controlCount, const QList<SimpleControlInfo>& controls)
{
    m_bindingInitialized = true;
    
    qDebug() << "EnhancedGripperSimple: 数据绑定初始化完成，绑定了" << controlCount << "个控件";
    
    // 打印控件信息
    for (const SimpleControlInfo& info : controls) {
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
    
    // 发射界面初始化完成信号
    emit interfaceInitialized(controlCount, controls);
}

void EnhancedGripperSimple::onDataChanged(int index, const QString& name, const QVariant& value, const QVariant& oldValue)
{
    qDebug() << QString("EnhancedGripperSimple: 参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(value.toString());
    
    // 发射参数变化信号给外部
    emit parameterChanged(index, name, value, oldValue);
}

void EnhancedGripperSimple::onButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) return;
    
    QString buttonName = button->objectName();
    QString buttonText = button->text();
    
    qDebug() << "EnhancedGripperSimple: 按钮点击" << buttonName << "(" << buttonText << ")";
    
    // 处理夹爪操作
    handleGripperOperation(buttonName);
}

void EnhancedGripperSimple::handleGripperOperation(const QString& buttonName)
{
    if (m_buttonOperationMap.contains(buttonName)) {
        QString operation = m_buttonOperationMap[buttonName];
        
        // 根据操作类型准备参数
        QVariant parameters;
        if (operation == "grip") {
            // 夹持操作可能需要力度参数
            parameters = QVariantMap{{"force", 50.0}, {"speed", 10.0}};
        } else if (operation == "open" || operation == "close") {
            // 开合操作可能需要位置和速度参数
            parameters = QVariantMap{{"position", 100.0}, {"speed", 20.0}};
        }
        
        qDebug() << "EnhancedGripperSimple: 发送夹爪操作请求" << operation;
        emit gripperOperationRequested(operation, parameters);
    }
}

void EnhancedGripperSimple::updateSimulatedData()
{
    if (!m_bindingInitialized || !m_binder) return;
    
    // 模拟一些状态数据的更新
    static int counter = 0;
    counter++;
    
    // 获取所有控件
    QList<SimpleControlInfo> controls = m_binder->getAllControls();
    
    if (!controls.isEmpty()) {
        // 随机更新一个控件的值（模拟外部系统更新）
        int randomIndex = counter % controls.size();
        const SimpleControlInfo& info = controls[randomIndex];
        
        QVariant newValue;
        if (info.type.contains("Spin")) {
            // 数值控件
            newValue = (counter * 7) % 100;
        } else if (info.type.contains("Check")) {
            // 复选框
            newValue = (counter % 2 == 0);
        } else if (info.type.contains("Combo")) {
            // 下拉框
            newValue = counter % 3;
        } else if (info.type.contains("Edit")) {
            // 文本框
            newValue = QString("模拟数据 %1").arg(counter);
        } else if (info.type.contains("Slider")) {
            // 滑块
            newValue = (counter * 13) % 101;
        }
        
        if (newValue.isValid()) {
            m_binder->updateValue(randomIndex, newValue);
            qDebug() << "EnhancedGripperSimple: 模拟更新控件" << info.name << "为" << newValue;
        }
    }
}

QMap<int, QVariant> EnhancedGripperSimple::getAllData() const
{
    QMap<int, QVariant> data;
    if (m_binder) {
        QList<SimpleControlInfo> controls = m_binder->getAllControls();
        for (const SimpleControlInfo& info : controls) {
            data[info.index] = info.value;
        }
    }
    return data;
}

bool EnhancedGripperSimple::updateData(int index, const QVariant& value)
{
    if (m_binder) {
        return m_binder->updateValue(index, value);
    }
    return false;
}

bool EnhancedGripperSimple::updateDataByName(const QString& name, const QVariant& value)
{
    if (m_binder) {
        return m_binder->updateValueByName(name, value);
    }
    return false;
}

QList<SimpleControlInfo> EnhancedGripperSimple::getControlList() const
{
    if (m_binder) {
        return m_binder->getAllControls();
    }
    return QList<SimpleControlInfo>();
}

void EnhancedGripperSimple::simulateGripperUpdate()
{
    // 手动触发一次模拟更新
    updateSimulatedData();
}

void EnhancedGripperSimple::resetParameters()
{
    if (!m_binder) return;
    
    // 重置所有参数为默认值
    QList<SimpleControlInfo> controls = m_binder->getAllControls();
    for (const SimpleControlInfo& info : controls) {
        if (info.type.contains("Spin")) {
            m_binder->updateValue(info.index, 0);
        } else if (info.type.contains("Check")) {
            m_binder->updateValue(info.index, false);
        } else if (info.type.contains("Combo")) {
            m_binder->updateValue(info.index, 0);
        } else if (info.type.contains("Edit")) {
            m_binder->updateValue(info.index, "");
        } else if (info.type.contains("Slider")) {
            m_binder->updateValue(info.index, 0);
        }
    }
    
    qDebug() << "EnhancedGripperSimple: 重置所有参数";
}
