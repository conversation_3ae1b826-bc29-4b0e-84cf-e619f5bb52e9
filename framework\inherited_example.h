#ifndef INHERITED_EXAMPLE_H
#define INHERITED_EXAMPLE_H

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QTextEdit>
#include <QTimer>
#include <QMessageBox>
#include "bindable_widget.h"

/**
 * @brief 基于继承的数据绑定示例
 * 
 * 演示如何通过继承BindableWidget来实现自动数据绑定：
 * 1. 继承BindableWidget而不是QWidget
 * 2. 在构造函数中调用initializeBinding()
 * 3. 重写onDataChanged()处理数据变化
 * 4. 重写onBindingInitialized()处理初始化完成
 */
class InheritedExample : public BindableWidget
{
    Q_OBJECT

public:
    explicit InheritedExample(QWidget *parent = nullptr);
    ~InheritedExample();

protected:
    /**
     * @brief 重写数据变化处理函数
     * 当任何绑定的控件数据发生变化时会自动调用
     */
    void onDataChanged(int index, const QString& name, 
                      const QVariant& newValue, const QVariant& oldValue) override;

    /**
     * @brief 重写绑定初始化完成处理函数
     * 当绑定初始化完成时会自动调用
     */
    void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls) override;

private slots:
    /**
     * @brief 显示控件表
     */
    void showControlTable();

    /**
     * @brief 模拟外部更新
     */
    void simulateExternalUpdate();

    /**
     * @brief 重置所有控件
     */
    void resetAllControls();

    /**
     * @brief 批量更新控件
     */
    void batchUpdateControls();

    /**
     * @brief 定时自动更新
     */
    void autoUpdate();

    /**
     * @brief 处理外部信号（演示信号槽通知）
     */
    void handleExternalSignal(int index, const QString& name, const QVariant& value);

private:
    /**
     * @brief 创建UI界面
     */
    void createUI();

    /**
     * @brief 创建控件组
     */
    QGroupBox* createControlGroup();

    /**
     * @brief 创建操作按钮组
     */
    QGroupBox* createButtonGroup();

    /**
     * @brief 创建日志显示组
     */
    QGroupBox* createLogGroup();

    /**
     * @brief 添加日志信息
     */
    void addLog(const QString& message);

    /**
     * @brief 模拟外部系统处理数据变化
     */
    void processDataChange(int index, const QString& name, const QVariant& value);

private:
    // UI控件（这些控件会被自动绑定）
    QLineEdit* m_nameEdit;
    QComboBox* m_typeCombo;
    QCheckBox* m_enabledCheck;
    QSpinBox* m_countSpin;
    QDoubleSpinBox* m_valueSpin;
    QSlider* m_progressSlider;

    // 操作按钮（这些不会被绑定，因为我们不需要监听它们的值变化）
    QPushButton* m_showTableButton;
    QPushButton* m_updateButton;
    QPushButton* m_resetButton;
    QPushButton* m_batchUpdateButton;

    // 日志显示
    QTextEdit* m_logDisplay;

    // 定时器
    QTimer* m_autoUpdateTimer;
    int m_updateCounter;

    // 控件序号映射（用于快速访问）
    QMap<QString, int> m_controlIndexMap;
};

#endif // INHERITED_EXAMPLE_H
